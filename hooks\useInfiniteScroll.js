import { useState, useEffect, useCallback } from 'react'

export function useInfiniteScroll(items, itemsPerPage = 12) {
  const [displayedItems, setDisplayedItems] = useState([])
  const [hasMore, setHasMore] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [page, setPage] = useState(1)

  // Initialize displayed items
  useEffect(() => {
    const initialItems = items.slice(0, itemsPerPage)
    setDisplayedItems(initialItems)
    setHasMore(items.length > itemsPerPage)
    setPage(1)
  }, [items, itemsPerPage])

  // Load more items
  const loadMore = useCallback(() => {
    if (isLoading || !hasMore) return

    setIsLoading(true)

    // Simulate loading delay for better UX
    setTimeout(() => {
      const nextPage = page + 1
      const startIndex = (nextPage - 1) * itemsPerPage
      const endIndex = startIndex + itemsPerPage
      const newItems = items.slice(startIndex, endIndex)

      if (newItems.length > 0) {
        setDisplayedItems(prev => [...prev, ...newItems])
        setPage(nextPage)
        setHasMore(endIndex < items.length)
      } else {
        setHasMore(false)
      }

      setIsLoading(false)
    }, 500) // 500ms delay for smooth loading experience
  }, [items, page, itemsPerPage, isLoading, hasMore])

  // Intersection Observer for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0]
        if (target.isIntersecting && hasMore && !isLoading) {
          loadMore()
        }
      },
      {
        root: null,
        rootMargin: '100px', // Start loading 100px before reaching the bottom
        threshold: 0.1
      }
    )

    const sentinel = document.getElementById('scroll-sentinel')
    if (sentinel) {
      observer.observe(sentinel)
    }

    return () => {
      if (sentinel) {
        observer.unobserve(sentinel)
      }
    }
  }, [loadMore, hasMore, isLoading])

  // Reset when items change (e.g., filtering)
  const reset = useCallback(() => {
    const initialItems = items.slice(0, itemsPerPage)
    setDisplayedItems(initialItems)
    setHasMore(items.length > itemsPerPage)
    setPage(1)
    setIsLoading(false)
  }, [items, itemsPerPage])

  return {
    displayedItems,
    hasMore,
    isLoading,
    loadMore,
    reset
  }
}
