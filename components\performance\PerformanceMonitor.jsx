'use client'

import { useEffect } from 'react'

export default function PerformanceMonitor() {
  useEffect(() => {
    // Only run in development
    if (process.env.NODE_ENV !== 'development') return

    // Web Vitals monitoring
    const reportWebVitals = (metric) => {
      console.log(`🚀 ${metric.name}:`, metric.value, metric.rating)
      
      // Log performance warnings
      if (metric.rating === 'poor') {
        console.warn(`⚠️ Poor ${metric.name} performance detected:`, metric.value)
      }
    }

    // Core Web Vitals
    const observeWebVitals = () => {
      // Largest Contentful Paint (LCP)
      if ('PerformanceObserver' in window) {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          
          const lcp = lastEntry.startTime
          const rating = lcp <= 2500 ? 'good' : lcp <= 4000 ? 'needs-improvement' : 'poor'
          
          reportWebVitals({
            name: 'LCP',
            value: Math.round(lcp),
            rating
          })
        })
        
        try {
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        } catch (e) {
          console.log('LCP observer not supported')
        }

        // First Input Delay (FID)
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry) => {
            const fid = entry.processingStart - entry.startTime
            const rating = fid <= 100 ? 'good' : fid <= 300 ? 'needs-improvement' : 'poor'
            
            reportWebVitals({
              name: 'FID',
              value: Math.round(fid),
              rating
            })
          })
        })
        
        try {
          fidObserver.observe({ entryTypes: ['first-input'] })
        } catch (e) {
          console.log('FID observer not supported')
        }

        // Cumulative Layout Shift (CLS)
        let clsValue = 0
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          })
          
          const rating = clsValue <= 0.1 ? 'good' : clsValue <= 0.25 ? 'needs-improvement' : 'poor'
          
          reportWebVitals({
            name: 'CLS',
            value: Math.round(clsValue * 1000) / 1000,
            rating
          })
        })
        
        try {
          clsObserver.observe({ entryTypes: ['layout-shift'] })
        } catch (e) {
          console.log('CLS observer not supported')
        }
      }

      // Time to Interactive (TTI) approximation
      setTimeout(() => {
        const navigationEntry = performance.getEntriesByType('navigation')[0]
        if (navigationEntry) {
          const tti = navigationEntry.loadEventEnd
          const rating = tti <= 3800 ? 'good' : tti <= 7300 ? 'needs-improvement' : 'poor'
          
          reportWebVitals({
            name: 'TTI',
            value: Math.round(tti),
            rating
          })
        }
      }, 1000)
    }

    // Bundle size monitoring
    const monitorBundleSize = () => {
      if ('performance' in window && 'getEntriesByType' in performance) {
        const resources = performance.getEntriesByType('resource')
        const jsResources = resources.filter(resource => 
          resource.name.includes('.js') && 
          (resource.name.includes('/_next/') || resource.name.includes('/static/'))
        )
        
        const totalJSSize = jsResources.reduce((total, resource) => {
          return total + (resource.transferSize || 0)
        }, 0)
        
        console.log(`📦 Total JS Bundle Size: ${(totalJSSize / 1024).toFixed(2)} KB`)
        
        if (totalJSSize > 500 * 1024) { // 500KB threshold
          console.warn('⚠️ Large bundle size detected. Consider code splitting.')
        }
      }
    }

    // Memory usage monitoring
    const monitorMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = performance.memory
        console.log(`🧠 Memory Usage:`, {
          used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
          total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
          limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`
        })
      }
    }

    // Run monitoring
    observeWebVitals()
    
    // Delayed monitoring for bundle and memory
    setTimeout(() => {
      monitorBundleSize()
      monitorMemoryUsage()
    }, 2000)

    // Periodic memory monitoring
    const memoryInterval = setInterval(monitorMemoryUsage, 30000) // Every 30 seconds

    return () => {
      clearInterval(memoryInterval)
    }
  }, [])

  // Don't render anything in production
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-black/80 text-white text-xs p-2 rounded-lg font-mono">
      <div className="text-lime-400">🚀 Performance Monitor Active</div>
      <div className="text-gray-300">Check console for metrics</div>
    </div>
  )
}
