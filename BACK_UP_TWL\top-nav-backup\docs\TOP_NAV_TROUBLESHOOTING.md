# 🔧 TWL Navigation Troubleshooting Guide

**Guide Version:** 1.0  
**Last Updated:** 2025-06-21  
**Support Level:** Production Ready  

## 🚨 **Common Issues & Solutions**

### **1. Navigation Not Displaying**

#### **Symptoms:**
- Navigation bar is completely missing
- White space where navigation should be
- Console errors about missing components

#### **Possible Causes & Solutions:**

**A. Missing Component Import**
```javascript
// ❌ Incorrect
import Header from '../components/layout/Header'

// ✅ Correct
import InteractiveHeader from '../components/layout/InteractiveHeader'
```

**B. Missing Context Providers**
```javascript
// ❌ Missing providers
<InteractiveHeader />

// ✅ Correct with providers
<TWLProviders>
  <InteractiveHeader />
</TWLProviders>
```

**C. CSS Not Loading**
```javascript
// Check that globals.css is imported in layout.jsx
import './globals.css'
```

### **2. Logo Images Not Displaying**

#### **Symptoms:**
- Broken image icons where logos should be
- Alt text showing instead of logos
- 404 errors in network tab

#### **Solutions:**

**A. Check File Paths**
```bash
# Verify files exist
ls public/twl.svg
ls public/logotwl.svg

# If missing, copy from backup
cp BACK_UP_TWL/top-nav-backup/public/twl.svg public/
cp BACK_UP_TWL/top-nav-backup/public/logotwl.svg public/
```

**B. Verify Image Paths in Component**
```javascript
// Check these paths in InteractiveHeader.jsx
<img src="/twl.svg" alt="TWL Logo" />
<img src="/logotwl.svg" alt="The White Laces" />
```

**C. Clear Next.js Cache**
```bash
rm -rf .next
npm run dev
```

### **3. Flag Icons Not Rendering**

#### **Symptoms:**
- Empty spaces where flags should be
- Console errors about FlagIcons
- Language dropdown shows no flags

#### **Solutions:**

**A. Check FlagIcons Import**
```javascript
// In InteractiveHeader.jsx
import { FlagIcons } from '../ui/FlagIcons'

// Verify file exists
ls components/ui/FlagIcons.jsx
```

**B. Verify Flag Component Usage**
```javascript
// Check this pattern in the component
{(() => {
  const FlagComponent = FlagIcons[selectedLanguage]
  return FlagComponent ? <FlagComponent size={20} /> : null
})()}
```

**C. SVG Rendering Issues**
```css
/* Add to globals.css if SVGs not rendering */
svg {
  display: block;
  max-width: 100%;
  height: auto;
}
```

### **4. Mobile Menu Not Working**

#### **Symptoms:**
- Mobile menu button doesn't respond
- Menu opens but doesn't close
- Menu content not displaying

#### **Solutions:**

**A. Check State Management**
```javascript
// Verify state is properly managed
const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

// Check button onClick
onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
```

**B. CSS Display Issues**
```css
/* Check responsive classes */
.xl:hidden  /* Should hide on desktop */
.hidden     /* Check if accidentally hidden */
```

**C. Z-Index Problems**
```css
/* Ensure proper layering */
.mobile-menu {
  z-index: 50;
  position: fixed;
}
```

### **5. Scroll Behavior Not Working**

#### **Symptoms:**
- Second navigation doesn't hide when scrolling
- Jerky scroll behavior
- Navigation appears/disappears randomly

#### **Solutions:**

**A. Check Scroll Event Listener**
```javascript
// Verify useEffect is properly set up
useEffect(() => {
  const handleScroll = () => {
    const currentScrollY = window.scrollY
    // ... scroll logic
  }
  
  window.addEventListener('scroll', handleScroll)
  return () => window.removeEventListener('scroll', handleScroll)
}, [lastScrollY]) // Important: dependency array
```

**B. Performance Issues**
```javascript
// Add throttling if scroll is too sensitive
const throttledScroll = useCallback(
  throttle(handleScroll, 16), // 60fps
  [lastScrollY]
)
```

**C. CSS Transition Issues**
```css
/* Ensure smooth transitions */
.nav-transition {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
```

### **6. Dropdown Menus Not Working**

#### **Symptoms:**
- Dropdowns don't open when clicked
- Dropdowns don't close when clicking outside
- Dropdown content not displaying

#### **Solutions:**

**A. Check Click Handlers**
```javascript
// Verify dropdown state management
const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState(false)

// Check button onClick
onClick={() => {
  setIsLanguageDropdownOpen(!isLanguageDropdownOpen)
  setIsCurrencyDropdownOpen(false) // Close other dropdown
}}
```

**B. Click Outside Detection**
```javascript
// Verify click outside handler
const handleClickOutside = (event) => {
  if (!event.target.closest('.language-dropdown') && 
      !event.target.closest('.currency-dropdown')) {
    setIsLanguageDropdownOpen(false)
    setIsCurrencyDropdownOpen(false)
  }
}
```

**C. CSS Positioning**
```css
/* Check dropdown positioning */
.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 50;
}
```

### **7. Layout Spacing Issues**

#### **Symptoms:**
- Content hidden behind navigation
- Too much/little space at top of page
- Navigation overlapping content

#### **Solutions:**

**A. Check Main Content Padding**
```javascript
// In layout.jsx
<main className="pt-32 lg:pt-36">
  {children}
</main>

// Mobile: 128px (32 * 4)
// Desktop: 144px (36 * 4)
```

**B. Navigation Heights**
```javascript
// Main header heights
<div className="h-16 lg:h-20">  // 64px mobile, 80px desktop

// Second nav height
<div className="h-16">           // 64px both
```

**C. Fixed Positioning**
```css
/* Verify navigation positioning */
.main-nav {
  position: fixed;
  top: 0;
  z-index: 50;
}

.second-nav {
  position: fixed;
  top: 4rem; /* lg:top-20 = 5rem */
  z-index: 40;
}
```

## 🔍 **Debugging Tools**

### **Browser Developer Tools**

**1. Console Errors**
```javascript
// Check for these common errors
- "Cannot read property of undefined"
- "FlagIcons is not defined"
- "TWLProviders context not found"
```

**2. Network Tab**
```bash
# Check for failed requests
- 404 errors for logo files
- Failed CSS/JS imports
- Missing font files
```

**3. Elements Inspector**
```css
/* Check computed styles */
- z-index values
- position properties
- display/visibility
- transform values
```

### **React Developer Tools**

**1. Component State**
```javascript
// Check state values in React DevTools
- isScrolled: boolean
- isMobileMenuOpen: boolean
- selectedLanguage: string
- selectedCurrency: string
```

**2. Context Values**
```javascript
// Verify context providers
- TWLProviders
- Cart context
- Wishlist context
- Auth context
```

### **Performance Debugging**

**1. Scroll Performance**
```javascript
// Add performance logging
const handleScroll = () => {
  console.time('scroll-handler')
  // ... scroll logic
  console.timeEnd('scroll-handler')
}
```

**2. Render Performance**
```javascript
// Check for unnecessary re-renders
React.memo(InteractiveHeader)
```

## 🛠️ **Quick Fixes**

### **Emergency Reset**
```bash
# If navigation is completely broken
1. Stop development server (Ctrl+C)
2. Clear Next.js cache: rm -rf .next
3. Reinstall dependencies: npm install
4. Restart server: npm run dev
```

### **Component Reset**
```bash
# Restore from backup
cp BACK_UP_TWL/top-nav-backup/components/layout/InteractiveHeader.jsx components/layout/
cp BACK_UP_TWL/top-nav-backup/components/ui/FlagIcons.jsx components/ui/
```

### **Asset Reset**
```bash
# Restore logo files
cp BACK_UP_TWL/top-nav-backup/public/twl.svg public/
cp BACK_UP_TWL/top-nav-backup/public/logotwl.svg public/
```

### **CSS Reset**
```bash
# Restore navigation styles
cp BACK_UP_TWL/top-nav-backup/app/globals.css app/
```

## 📞 **Support Checklist**

Before seeking help, verify:

- [ ] All files are in correct locations
- [ ] No console errors in browser
- [ ] TWLProviders is wrapping the component
- [ ] Logo files exist in public folder
- [ ] FlagIcons component is imported correctly
- [ ] Main content has proper padding
- [ ] Browser cache is cleared
- [ ] Development server is running
- [ ] No conflicting CSS rules
- [ ] All dependencies are installed

## 🔄 **Recovery Procedures**

### **Complete Restoration**
```bash
# 1. Backup current broken files
mkdir broken-backup
cp -r components/layout broken-backup/
cp -r components/ui broken-backup/
cp app/layout.jsx broken-backup/
cp app/globals.css broken-backup/

# 2. Restore from working backup
cp -r BACK_UP_TWL/top-nav-backup/components/* components/
cp BACK_UP_TWL/top-nav-backup/app/layout.jsx app/
cp BACK_UP_TWL/top-nav-backup/app/globals.css app/
cp BACK_UP_TWL/top-nav-backup/public/* public/

# 3. Restart development
rm -rf .next
npm run dev
```

### **Partial Restoration**
```bash
# Restore only specific components
cp BACK_UP_TWL/top-nav-backup/components/layout/InteractiveHeader.jsx components/layout/
# or
cp BACK_UP_TWL/top-nav-backup/components/ui/FlagIcons.jsx components/ui/
```

---

**🎯 This troubleshooting guide covers 95% of common navigation issues. For complex problems, refer to the implementation notes and component documentation.**
