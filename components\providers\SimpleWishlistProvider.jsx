'use client'

import { createContext, useContext, useState, useEffect } from 'react'

const WishlistContext = createContext()

export function SimpleWishlistProvider({ children }) {
  const [wishlistItems, setWishlistItems] = useState([])
  const [isHydrated, setIsHydrated] = useState(false)

  // Hydration effect
  useEffect(() => {
    setIsHydrated(true)
  }, [])

  // Load wishlist from localStorage on mount
  useEffect(() => {
    if (!isHydrated) return

    try {
      const savedWishlist = localStorage.getItem('twl-simple-wishlist')
      if (savedWishlist) {
        const parsedWishlist = JSON.parse(savedWishlist)
        setWishlistItems(parsedWishlist)
      }
    } catch (error) {
      console.error('Error loading wishlist from localStorage:', error)
    }
  }, [isHydrated])

  // Save wishlist to localStorage whenever it changes
  useEffect(() => {
    if (!isHydrated) return

    try {
      localStorage.setItem('twl-simple-wishlist', JSON.stringify(wishlistItems))
    } catch (error) {
      console.error('Error saving wishlist to localStorage:', error)
    }
  }, [isHydrated, wishlistItems])

  // Wishlist Actions
  const addToWishlist = (product) => {
    const productId = product.id || product.productId || Date.now()
    
    setWishlistItems(prevItems => {
      // Check if item already exists
      const existingItem = prevItems.find(item => item.productId === productId)
      
      if (existingItem) {
        // Item already in wishlist, don't add duplicate
        showToast('Este producto ya está en tus favoritos')
        return prevItems
      }
      
      // Add new item
      const newItem = {
        id: `wishlist-${productId}`,
        productId,
        name: product.name || 'Producto',
        brand: product.brand || 'Marca',
        image: product.image || '/placeholder-shoe.jpg',
        price: product.price || 0,
        addedAt: new Date().toISOString()
      }
      
      showToast(`¡${product.name || 'Producto'} agregado a favoritos!`)
      return [...prevItems, newItem]
    })
  }

  const removeFromWishlist = (productId) => {
    setWishlistItems(prevItems => {
      const item = prevItems.find(item => item.productId === productId)
      const filteredItems = prevItems.filter(item => item.productId !== productId)
      
      if (item) {
        showToast(`${item.name} eliminado de favoritos`)
      }
      
      return filteredItems
    })
  }

  const toggleWishlist = (product) => {
    const productId = product.id || product.productId
    const isInWishlist = wishlistItems.some(item => item.productId === productId)
    
    if (isInWishlist) {
      removeFromWishlist(productId)
    } else {
      addToWishlist(product)
    }
  }

  const isInWishlist = (productId) => {
    return wishlistItems.some(item => item.productId === productId)
  }

  const clearWishlist = () => {
    setWishlistItems([])
    showToast('Lista de favoritos vaciada')
  }

  // Wishlist Calculations
  const getWishlistCount = () => {
    return wishlistItems.length
  }

  const getWishlistValue = () => {
    return wishlistItems.reduce((total, item) => total + (item.price || 0), 0)
  }

  // Simple toast notification
  const showToast = (message) => {
    const toast = document.createElement('div')
    toast.className = 'fixed top-20 right-4 bg-lime-500 text-black px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300'
    toast.textContent = message
    document.body.appendChild(toast)
    
    // Animate in
    setTimeout(() => {
      toast.style.transform = 'translateX(0)'
      toast.style.opacity = '1'
    }, 100)
    
    // Remove after 3 seconds
    setTimeout(() => {
      toast.style.transform = 'translateX(100%)'
      toast.style.opacity = '0'
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast)
        }
      }, 300)
    }, 3000)
  }

  const value = {
    // State
    items: wishlistItems,
    
    // Actions
    addToWishlist,
    removeFromWishlist,
    toggleWishlist,
    clearWishlist,
    
    // Utilities
    isInWishlist,
    getWishlistCount,
    getWishlistValue,
    showToast
  }

  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  )
}

export function useSimpleWishlist() {
  const context = useContext(WishlistContext)
  
  if (!context) {
    throw new Error('useSimpleWishlist must be used within a SimpleWishlistProvider')
  }
  
  return context
}
