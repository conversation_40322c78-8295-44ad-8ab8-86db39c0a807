'use client'

import { createContext, useContext, useState, useEffect } from 'react'

const CartContext = createContext()

export function SimpleCartProvider({ children }) {
  const [cartItems, setCartItems] = useState([])
  const [isHydrated, setIsHydrated] = useState(false)

  // Hydration effect
  useEffect(() => {
    setIsHydrated(true)
  }, [])

  // Load cart from localStorage on mount
  useEffect(() => {
    if (!isHydrated) return

    try {
      const savedCart = localStorage.getItem('twl-simple-cart')
      if (savedCart) {
        const parsedCart = JSON.parse(savedCart)
        setCartItems(parsedCart)
      }
    } catch (error) {
      console.error('Error loading cart from localStorage:', error)
    }
  }, [isHydrated])

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    if (!isHydrated) return

    try {
      localStorage.setItem('twl-simple-cart', JSON.stringify(cartItems))
    } catch (error) {
      console.error('Error saving cart to localStorage:', error)
    }
  }, [isHydrated, cartItems])

  // Cart Actions
  const addItem = (product, size = 'M', quantity = 1) => {
    const itemId = `${product.id || Date.now()}-${size}`
    
    setCartItems(prevItems => {
      const existingItemIndex = prevItems.findIndex(item => item.id === itemId)
      
      if (existingItemIndex >= 0) {
        // Update existing item quantity
        const updatedItems = [...prevItems]
        updatedItems[existingItemIndex].quantity += quantity
        return updatedItems
      } else {
        // Add new item
        const newItem = {
          id: itemId,
          productId: product.id || Date.now(),
          name: product.name || 'Producto',
          brand: product.brand || 'Marca',
          image: product.image || '/placeholder-shoe.jpg',
          size,
          quantity,
          price: product.price || 0,
          addedAt: new Date().toISOString()
        }
        return [...prevItems, newItem]
      }
    })

    // Show toast notification
    showToast(`¡${product.name || 'Producto'} agregado al carrito!`)
  }

  const removeItem = (itemId) => {
    setCartItems(prevItems => prevItems.filter(item => item.id !== itemId))
    showToast('Producto eliminado del carrito')
  }

  const updateQuantity = (itemId, quantity) => {
    if (quantity <= 0) {
      removeItem(itemId)
      return
    }

    setCartItems(prevItems =>
      prevItems.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      )
    )
  }

  const clearCart = () => {
    setCartItems([])
    showToast('Carrito vaciado')
  }

  // Cart Calculations
  const getItemsCount = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0)
  }

  const getSubtotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0)
  }

  const getTax = () => {
    // 16% IVA for Mexico
    return getSubtotal() * 0.16
  }

  const getShipping = () => {
    const subtotal = getSubtotal()
    // Free shipping over $3000 MXN
    return subtotal >= 3000 ? 0 : 200
  }

  const getTotal = () => {
    return getSubtotal() + getTax() + getShipping()
  }

  // Simple toast notification
  const showToast = (message) => {
    const toast = document.createElement('div')
    toast.className = 'fixed top-20 right-4 bg-lime-500 text-black px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300'
    toast.textContent = message
    document.body.appendChild(toast)
    
    // Animate in
    setTimeout(() => {
      toast.style.transform = 'translateX(0)'
      toast.style.opacity = '1'
    }, 100)
    
    // Remove after 3 seconds
    setTimeout(() => {
      toast.style.transform = 'translateX(100%)'
      toast.style.opacity = '0'
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast)
        }
      }, 300)
    }, 3000)
  }

  const value = {
    // State
    items: cartItems,
    
    // Actions
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    
    // Calculations
    getItemsCount,
    getSubtotal,
    getTax,
    getShipping,
    getTotal,
    
    // Utilities
    showToast
  }

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  )
}

export function useSimpleCart() {
  const context = useContext(CartContext)
  
  if (!context) {
    throw new Error('useSimpleCart must be used within a SimpleCartProvider')
  }
  
  return context
}
