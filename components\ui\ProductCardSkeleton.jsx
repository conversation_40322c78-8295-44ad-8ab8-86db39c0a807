'use client'

export default function ProductCardSkeleton() {
  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden animate-pulse">
      {/* Image Skeleton */}
      <div className="relative aspect-square bg-gray-200">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-shimmer"></div>
        
        {/* Wishlist button skeleton */}
        <div className="absolute top-3 right-3 w-8 h-8 bg-gray-300 rounded-full"></div>
        
        {/* Cart button skeleton */}
        <div className="absolute bottom-3 right-3 w-10 h-10 bg-gray-300 rounded-full"></div>
      </div>
      
      {/* Content Skeleton */}
      <div className="p-4 space-y-3">
        {/* Brand name skeleton */}
        <div className="h-3 bg-gray-200 rounded w-16"></div>
        
        {/* Product name skeleton */}
        <div className="space-y-2">
          <div className="h-4 bg-gray-200 rounded w-full"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        </div>
        
        {/* Price skeleton */}
        <div className="flex items-baseline space-x-2">
          <div className="h-5 bg-gray-200 rounded w-20"></div>
          <div className="h-4 bg-gray-200 rounded w-16"></div>
        </div>
      </div>
    </div>
  )
}

// Multiple skeletons for loading state
export function ProductCardSkeletons({ count = 8 }) {
  return (
    <>
      {Array.from({ length: count }, (_, index) => (
        <ProductCardSkeleton key={`skeleton-${index}`} />
      ))}
    </>
  )
}
