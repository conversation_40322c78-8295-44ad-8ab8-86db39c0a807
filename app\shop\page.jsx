'use client'

import { useState, useEffect, useMemo } from 'react'
import { useSimpleCart, useSimpleWishlist } from '../../components/providers/TWLProviders'
import OptimizedProductCard from '../../components/performance/OptimizedProductCard'



export default function ShopPage() {
  const [products, setProducts] = useState([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedBrand, setSelectedBrand] = useState('all')
  const [sortBy, setSortBy] = useState('newest')

  // Mock products data
  useEffect(() => {
    const mockProducts = [
      { id: 1, name: 'Nike Air Force 1 x Gucci', brand: 'Nike', category: 'sneakers', price: 4500, originalPrice: 5200, isLimited: true, description: 'Colaboración exclusiva entre Nike y Gucci' },
      { id: 2, name: 'Dior B23 High-Top', brand: 'Dior', category: 'sneakers', price: 22000, isExclusive: true, description: 'Sneakers de lujo con diseño oblique' },
      { id: 3, name: '<PERSON> Trainer', brand: 'Louis Vuitton', category: 'sneakers', price: 28500, isNew: true, description: 'Trainer de lujo con monograma LV' },
      { id: 4, name: 'Gucci Ace Sneaker', brand: 'Gucci', category: 'sneakers', price: 12800, description: 'Sneaker clásico con detalles de abeja' },
      { id: 5, name: 'Yeezy Boost 350 V2', brand: 'Adidas', category: 'sneakers', price: 4800, isLimited: true, description: 'Diseño icónico de Kanye West' },
      { id: 6, name: 'Golden Goose Superstar', brand: 'Golden Goose', category: 'sneakers', price: 8900, description: 'Sneakers con acabado vintage' },
      { id: 7, name: 'Balenciaga Triple S', brand: 'Balenciaga', category: 'sneakers', price: 15600, isExclusive: true, description: 'Chunky sneaker de lujo' },
      { id: 8, name: 'Off-White Dunk Low', brand: 'Nike', category: 'sneakers', price: 6200, originalPrice: 7000, isLimited: true, description: 'Colaboración con Virgil Abloh' },
      { id: 9, name: 'Hermès Oran Sandals', brand: 'Hermès', category: 'sandals', price: 18500, description: 'Sandalias de lujo en cuero' },
      { id: 10, name: 'Louboutin Pigalle', brand: 'Christian Louboutin', category: 'heels', price: 16800, description: 'Tacones icónicos con suela roja' },
      { id: 11, name: 'Manolo Blahnik Hangisi', brand: 'Manolo Blahnik', category: 'heels', price: 19200, description: 'Tacones con cristal Swarovski' },
      { id: 12, name: 'Tom Ford Oxford', brand: 'Tom Ford', category: 'formal', price: 24500, description: 'Zapatos formales de lujo' }
    ]
    setProducts(mockProducts)
  }, [])

  // Optimized filtering and sorting with useMemo
  const filteredProducts = useMemo(() => {
    let filtered = [...products]

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.brand.toLowerCase().includes(query) ||
        product.description?.toLowerCase().includes(query)
      )
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === selectedCategory)
    }

    // Apply brand filter
    if (selectedBrand !== 'all') {
      filtered = filtered.filter(product => product.brand === selectedBrand)
    }

    // Apply sorting
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price)
        break
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price)
        break
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name))
        break
      case 'brand':
        filtered.sort((a, b) => a.brand.localeCompare(b.brand))
        break
      default: // newest
        filtered.sort((a, b) => b.id - a.id)
    }

    return filtered
  }, [products, searchQuery, selectedCategory, selectedBrand, sortBy])

  const categories = [
    { id: 'all', name: 'Todos' },
    { id: 'sneakers', name: 'Sneakers' },
    { id: 'sandals', name: 'Sandalias' },
    { id: 'heels', name: 'Tacones' },
    { id: 'formal', name: 'Formales' }
  ]

  const brands = [
    { id: 'all', name: 'Todas las marcas' },
    { id: 'Nike', name: 'Nike' },
    { id: 'Adidas', name: 'Adidas' },
    { id: 'Gucci', name: 'Gucci' },
    { id: 'Dior', name: 'Dior' },
    { id: 'Louis Vuitton', name: 'Louis Vuitton' },
    { id: 'Balenciaga', name: 'Balenciaga' }
  ]

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-black mb-4">
            Tienda TWL
          </h1>
          <p className="text-lg text-gray-600">
            Descubre nuestra colección exclusiva de calzado de lujo
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Buscar</label>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Buscar productos..."
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500"
              />
            </div>

            {/* Category */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Categoría</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500"
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>{category.name}</option>
                ))}
              </select>
            </div>

            {/* Brand */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Marca</label>
              <select
                value={selectedBrand}
                onChange={(e) => setSelectedBrand(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500"
              >
                {brands.map(brand => (
                  <option key={brand.id} value={brand.id}>{brand.name}</option>
                ))}
              </select>
            </div>

            {/* Sort */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Ordenar por</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500"
              >
                <option value="newest">Más recientes</option>
                <option value="price-low">Precio: menor a mayor</option>
                <option value="price-high">Precio: mayor a menor</option>
                <option value="name">Nombre A-Z</option>
                <option value="brand">Marca A-Z</option>
              </select>
            </div>
          </div>
        </div>

        {/* Results count */}
        <div className="mb-6">
          <p className="text-gray-600">
            {filteredProducts.length} productos encontrados
          </p>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
          {filteredProducts.map((product, index) => (
            <OptimizedProductCard
              key={product.id}
              product={product}
              priority={index < 8} // Prioritize first 8 products for faster loading
            />
          ))}
        </div>

        {/* No results */}
        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">👟</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No se encontraron productos
            </h3>
            <p className="text-gray-600 mb-4">
              Intenta ajustar tus filtros o buscar algo diferente
            </p>
            <button
              onClick={() => {
                setSearchQuery('')
                setSelectedCategory('all')
                setSelectedBrand('all')
                setSortBy('newest')
              }}
              className="px-6 py-3 bg-lime-500 hover:bg-lime-600 text-black rounded-lg font-medium transition-colors"
            >
              Limpiar Filtros
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
