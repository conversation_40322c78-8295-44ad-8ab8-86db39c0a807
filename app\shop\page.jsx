'use client'

import { useState, useEffect } from 'react'
import { useSimpleCart, useSimpleWishlist } from '../../components/providers/TWLProviders'

// Simple Product Card Component for Shop
function ShopProductCard({ product }) {
  const { addItem } = useSimpleCart()
  const { toggleWishlist, isInWishlist } = useSimpleWishlist()

  const handleAddToCart = () => {
    addItem(product, 'M', 1)
  }

  const handleToggleWishlist = () => {
    toggleWishlist(product)
  }

  const isWishlisted = isInWishlist(product.id)

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
      <div className="relative h-64 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
        {/* Product badges */}
        <div className="absolute top-3 left-3 z-10">
          {product.isLimited && (
            <span className="px-2 py-1 bg-red-500 text-white text-xs font-medium rounded-full">
              LIMITADO
            </span>
          )}
          {product.isExclusive && (
            <span className="px-2 py-1 bg-purple-500 text-white text-xs font-medium rounded-full">
              EXCLUSIVO
            </span>
          )}
          {product.isNew && (
            <span className="px-2 py-1 bg-green-500 text-white text-xs font-medium rounded-full">
              NUEVO
            </span>
          )}
        </div>

        {/* Wishlist button */}
        <button
          onClick={handleToggleWishlist}
          className={`absolute top-3 right-3 p-2 backdrop-blur-sm rounded-full hover:bg-white transition-all duration-300 ${
            isWishlisted ? 'bg-red-100 text-red-500' : 'bg-white/80 text-gray-600'
          }`}
        >
          <svg className="w-5 h-5" fill={isWishlisted ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button>

        {/* Quick view overlay */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <button className="px-6 py-3 bg-white text-black rounded-lg font-medium hover:bg-gray-100 transition-colors">
            Vista Rápida
          </button>
        </div>
      </div>

      <div className="p-6">
        <div className="mb-3">
          <p className="text-xs text-gray-500 uppercase tracking-wide font-medium">{product.brand}</p>
          <h3 className="font-semibold text-gray-900 text-lg leading-tight">{product.name}</h3>
          <p className="text-sm text-gray-600 mt-1">{product.description}</p>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex flex-col">
            <span className="text-xl font-bold text-lime-600">${product.price.toLocaleString()} MXN</span>
            {product.originalPrice && (
              <span className="text-sm text-gray-400 line-through">${product.originalPrice.toLocaleString()} MXN</span>
            )}
          </div>
          <button
            onClick={handleAddToCart}
            className="px-6 py-2 bg-lime-500 hover:bg-lime-600 text-black rounded-lg font-medium transition-all duration-300 hover:scale-105"
          >
            Agregar al Carrito
          </button>
        </div>
      </div>
    </div>
  )
}

export default function ShopPage() {
  const [products, setProducts] = useState([])
  const [filteredProducts, setFilteredProducts] = useState([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedBrand, setSelectedBrand] = useState('all')
  const [sortBy, setSortBy] = useState('newest')

  // Mock products data
  useEffect(() => {
    const mockProducts = [
      { id: 1, name: 'Nike Air Force 1 x Gucci', brand: 'Nike', category: 'sneakers', price: 4500, originalPrice: 5200, isLimited: true, description: 'Colaboración exclusiva entre Nike y Gucci' },
      { id: 2, name: 'Dior B23 High-Top', brand: 'Dior', category: 'sneakers', price: 22000, isExclusive: true, description: 'Sneakers de lujo con diseño oblique' },
      { id: 3, name: 'Louis Vuitton Trainer', brand: 'Louis Vuitton', category: 'sneakers', price: 28500, isNew: true, description: 'Trainer de lujo con monograma LV' },
      { id: 4, name: 'Gucci Ace Sneaker', brand: 'Gucci', category: 'sneakers', price: 12800, description: 'Sneaker clásico con detalles de abeja' },
      { id: 5, name: 'Yeezy Boost 350 V2', brand: 'Adidas', category: 'sneakers', price: 4800, isLimited: true, description: 'Diseño icónico de Kanye West' },
      { id: 6, name: 'Golden Goose Superstar', brand: 'Golden Goose', category: 'sneakers', price: 8900, description: 'Sneakers con acabado vintage' },
      { id: 7, name: 'Balenciaga Triple S', brand: 'Balenciaga', category: 'sneakers', price: 15600, isExclusive: true, description: 'Chunky sneaker de lujo' },
      { id: 8, name: 'Off-White Dunk Low', brand: 'Nike', category: 'sneakers', price: 6200, originalPrice: 7000, isLimited: true, description: 'Colaboración con Virgil Abloh' },
      { id: 9, name: 'Hermès Oran Sandals', brand: 'Hermès', category: 'sandals', price: 18500, description: 'Sandalias de lujo en cuero' },
      { id: 10, name: 'Louboutin Pigalle', brand: 'Christian Louboutin', category: 'heels', price: 16800, description: 'Tacones icónicos con suela roja' },
      { id: 11, name: 'Manolo Blahnik Hangisi', brand: 'Manolo Blahnik', category: 'heels', price: 19200, description: 'Tacones con cristal Swarovski' },
      { id: 12, name: 'Tom Ford Oxford', brand: 'Tom Ford', category: 'formal', price: 24500, description: 'Zapatos formales de lujo' }
    ]
    setProducts(mockProducts)
    setFilteredProducts(mockProducts)
  }, [])

  // Filter and sort products
  useEffect(() => {
    let filtered = [...products]

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === selectedCategory)
    }

    // Apply brand filter
    if (selectedBrand !== 'all') {
      filtered = filtered.filter(product => product.brand === selectedBrand)
    }

    // Apply sorting
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price)
        break
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price)
        break
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name))
        break
      case 'brand':
        filtered.sort((a, b) => a.brand.localeCompare(b.brand))
        break
      default: // newest
        filtered.sort((a, b) => b.id - a.id)
    }

    setFilteredProducts(filtered)
  }, [products, searchQuery, selectedCategory, selectedBrand, sortBy])

  const categories = [
    { id: 'all', name: 'Todos' },
    { id: 'sneakers', name: 'Sneakers' },
    { id: 'sandals', name: 'Sandalias' },
    { id: 'heels', name: 'Tacones' },
    { id: 'formal', name: 'Formales' }
  ]

  const brands = [
    { id: 'all', name: 'Todas las marcas' },
    { id: 'Nike', name: 'Nike' },
    { id: 'Adidas', name: 'Adidas' },
    { id: 'Gucci', name: 'Gucci' },
    { id: 'Dior', name: 'Dior' },
    { id: 'Louis Vuitton', name: 'Louis Vuitton' },
    { id: 'Balenciaga', name: 'Balenciaga' }
  ]

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-black mb-4">
            Tienda TWL
          </h1>
          <p className="text-lg text-gray-600">
            Descubre nuestra colección exclusiva de calzado de lujo
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Buscar</label>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Buscar productos..."
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500"
              />
            </div>

            {/* Category */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Categoría</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500"
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>{category.name}</option>
                ))}
              </select>
            </div>

            {/* Brand */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Marca</label>
              <select
                value={selectedBrand}
                onChange={(e) => setSelectedBrand(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500"
              >
                {brands.map(brand => (
                  <option key={brand.id} value={brand.id}>{brand.name}</option>
                ))}
              </select>
            </div>

            {/* Sort */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Ordenar por</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500"
              >
                <option value="newest">Más recientes</option>
                <option value="price-low">Precio: menor a mayor</option>
                <option value="price-high">Precio: mayor a menor</option>
                <option value="name">Nombre A-Z</option>
                <option value="brand">Marca A-Z</option>
              </select>
            </div>
          </div>
        </div>

        {/* Results count */}
        <div className="mb-6">
          <p className="text-gray-600">
            {filteredProducts.length} productos encontrados
          </p>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProducts.map((product) => (
            <ShopProductCard key={product.id} product={product} />
          ))}
        </div>

        {/* No results */}
        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">👟</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No se encontraron productos
            </h3>
            <p className="text-gray-600 mb-4">
              Intenta ajustar tus filtros o buscar algo diferente
            </p>
            <button
              onClick={() => {
                setSearchQuery('')
                setSelectedCategory('all')
                setSelectedBrand('all')
                setSortBy('newest')
              }}
              className="px-6 py-3 bg-lime-500 hover:bg-lime-600 text-black rounded-lg font-medium transition-colors"
            >
              Limpiar Filtros
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
