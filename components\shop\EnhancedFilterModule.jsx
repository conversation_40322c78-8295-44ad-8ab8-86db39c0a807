'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

export default function EnhancedFilterSidebar({
  searchQuery,
  setSearchQuery,
  selectedCategory,
  setSelectedCategory,
  selectedBrand,
  setSelectedBrand,
  sortBy,
  setSortBy,
  priceRange,
  setPriceRange,
  selectedSizes,
  setSelectedSizes,
  selectedColors,
  setSelectedColors,
  isOnSale,
  setIsOnSale,
  isLimited,
  setIsLimited,
  isNew,
  setIsNew,
  onClearAll,
  isOpen,
  setIsOpen
}) {
  const [activeFilters, setActiveFilters] = useState(0)
  const [expandedSections, setExpandedSections] = useState({
    categories: true,
    brands: false,
    price: false,
    sizes: false,
    colors: false
  })

  // Categories with icons
  const categories = [
    { id: 'all', name: 'Todos', icon: '🏪', count: 497 },
    { id: 'sneakers', name: 'Snea<PERSON>', icon: '👟', count: 245 },
    { id: 'sandals', name: '<PERSON><PERSON><PERSON>', icon: '🩴', count: 89 },
    { id: 'heels', name: '<PERSON>con<PERSON>', icon: '👠', count: 67 },
    { id: 'formal', name: 'Formales', icon: '👞', count: 96 }
  ]

  // Luxury brands
  const brands = [
    { id: 'all', name: 'Todas', count: 497 },
    { id: 'Nike', name: 'Nike', count: 89 },
    { id: 'Adidas', name: 'Adidas', count: 67 },
    { id: 'Gucci', name: 'Gucci', count: 45 },
    { id: 'Dior', name: 'Dior', count: 34 },
    { id: 'Louis Vuitton', name: 'LV', count: 28 },
    { id: 'Balenciaga', name: 'Balenciaga', count: 23 },
    { id: 'Off-White', name: 'Off-White', count: 19 },
    { id: 'Golden Goose', name: 'Golden Goose', count: 15 }
  ]

  // Available sizes
  const sizes = ['35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45']

  // Available colors
  const colors = [
    { id: 'black', name: 'Negro', hex: '#000000' },
    { id: 'white', name: 'Blanco', hex: '#FFFFFF' },
    { id: 'red', name: 'Rojo', hex: '#DC2626' },
    { id: 'blue', name: 'Azul', hex: '#2563EB' },
    { id: 'green', name: 'Verde', hex: '#16A34A' },
    { id: 'yellow', name: 'Amarillo', hex: '#EAB308' },
    { id: 'pink', name: 'Rosa', hex: '#EC4899' },
    { id: 'brown', name: 'Marrón', hex: '#A16207' }
  ]

  // Toggle section expansion
  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  // Count active filters
  useEffect(() => {
    let count = 0
    if (searchQuery) count++
    if (selectedCategory !== 'all') count++
    if (selectedBrand !== 'all') count++
    if (priceRange[0] > 0 || priceRange[1] < 50000) count++
    if (selectedSizes.length > 0) count++
    if (selectedColors.length > 0) count++
    if (isOnSale) count++
    if (isLimited) count++
    if (isNew) count++
    setActiveFilters(count)
  }, [searchQuery, selectedCategory, selectedBrand, priceRange, selectedSizes, selectedColors, isOnSale, isLimited, isNew])

  const handleSizeToggle = (size) => {
    setSelectedSizes(prev => 
      prev.includes(size) 
        ? prev.filter(s => s !== size)
        : [...prev, size]
    )
  }

  const handleColorToggle = (colorId) => {
    setSelectedColors(prev => 
      prev.includes(colorId) 
        ? prev.filter(c => c !== colorId)
        : [...prev, colorId]
    )
  }

  return (
    <>
      {/* Mobile Filter Toggle */}
      <div className="lg:hidden fixed top-20 left-4 z-50">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-12 h-12 bg-lime-green hover:bg-lime-green-dark text-black rounded-full shadow-lg flex items-center justify-center transition-all duration-300"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
          </svg>
        </button>
      </div>

      {/* Overlay for mobile */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setIsOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Filter Sidebar */}
      <motion.div
        initial={{ x: -320 }}
        animate={{ x: isOpen ? 0 : -320 }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className={`fixed lg:sticky top-0 left-0 h-screen w-80 bg-white shadow-2xl border-r border-gray-100 z-50 lg:z-auto overflow-y-auto ${
          isOpen ? 'lg:translate-x-0' : 'lg:translate-x-0'
        }`}
        style={{ top: '80px', height: 'calc(100vh - 80px)' }}
      >
        {/* Filter Header */}
        <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-lime-green/10 to-lime-green/5">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-lime-green rounded-xl flex items-center justify-center shadow-md">
                <svg className="w-5 h-5 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-bold text-black font-poppins">Filtros</h3>
                <p className="text-xs text-gray-500">
                  {activeFilters > 0 ? `${activeFilters} activos` : 'Personaliza tu búsqueda'}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {activeFilters > 0 && (
                <button
                  onClick={onClearAll}
                  className="px-3 py-1 text-xs text-red-600 hover:bg-red-50 rounded-lg transition-colors font-medium"
                >
                  Limpiar
                </button>
              )}
              <button
                onClick={() => setIsOpen(false)}
                className="lg:hidden p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Quick Filters */}
        <div className="p-4 border-b border-gray-100">
          <div className="grid grid-cols-1 gap-2">
            <button
              onClick={() => setIsOnSale(!isOnSale)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 text-left ${
                isOnSale
                  ? 'bg-lime-green text-black shadow-sm'
                  : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
              }`}
            >
              🏷️ En Oferta
            </button>
            <button
              onClick={() => setIsLimited(!isLimited)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 text-left ${
                isLimited
                  ? 'bg-red-500 text-white shadow-sm'
                  : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
              }`}
            >
              ⭐ Edición Limitada
            </button>
            <button
              onClick={() => setIsNew(!isNew)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 text-left ${
                isNew
                  ? 'bg-blue-500 text-white shadow-sm'
                  : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
              }`}
            >
              ✨ Nuevos
            </button>
          </div>
        </div>

        {/* Main Filter Content */}
        <div className="flex-1 overflow-y-auto">
          {/* Search Bar */}
          <div className="p-4 border-b border-gray-100">
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Buscar productos..."
                className="w-full px-4 py-3 pl-10 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-green focus:border-transparent transition-all duration-300 text-sm"
              />
              <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>

          {/* Categories Section */}
          <div className="border-b border-gray-100">
            <button
              onClick={() => toggleSection('categories')}
              className="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <span className="text-lg">📂</span>
                <span className="font-semibold text-gray-900 font-poppins">Categorías</span>
              </div>
              <motion.svg
                animate={{ rotate: expandedSections.categories ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className="w-4 h-4 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </motion.svg>
            </button>
            <AnimatePresence>
              {expandedSections.categories && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="p-4 pt-0 space-y-2">
                    {categories.map(category => (
                      <button
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                        className={`w-full p-3 rounded-lg transition-all duration-300 text-left flex items-center justify-between group ${
                          selectedCategory === category.id
                            ? 'bg-lime-green text-black shadow-sm'
                            : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <span className="text-lg">{category.icon}</span>
                          <span className="font-medium">{category.name}</span>
                        </div>
                        <span className="text-xs opacity-70">{category.count}</span>
                      </button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Brands Section */}
          <div className="border-b border-gray-100">
            <button
              onClick={() => toggleSection('brands')}
              className="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <span className="text-lg">🏷️</span>
                <span className="font-semibold text-gray-900 font-poppins">Marcas</span>
              </div>
              <motion.svg
                animate={{ rotate: expandedSections.brands ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className="w-4 h-4 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </motion.svg>
            </button>
            <AnimatePresence>
              {expandedSections.brands && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="p-4 pt-0 space-y-2">
                    {brands.map(brand => (
                      <button
                        key={brand.id}
                        onClick={() => setSelectedBrand(brand.id)}
                        className={`w-full p-3 rounded-lg transition-all duration-300 text-left flex items-center justify-between ${
                          selectedBrand === brand.id
                            ? 'bg-lime-green text-black shadow-sm'
                            : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <span className="font-medium">{brand.name}</span>
                        <span className="text-xs opacity-70">{brand.count}</span>
                      </button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Price Range Section */}
          <div className="border-b border-gray-100">
            <button
              onClick={() => toggleSection('price')}
              className="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <span className="text-lg">💰</span>
                <span className="font-semibold text-gray-900 font-poppins">Precio</span>
              </div>
              <motion.svg
                animate={{ rotate: expandedSections.price ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className="w-4 h-4 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </motion.svg>
            </button>
            <AnimatePresence>
              {expandedSections.price && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="p-4 pt-0 space-y-4">
                    <div className="space-y-3">
                      <input
                        type="range"
                        min="0"
                        max="50000"
                        value={priceRange[0]}
                        onChange={(e) => setPriceRange([parseInt(e.target.value), priceRange[1]])}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                      />
                      <input
                        type="range"
                        min="0"
                        max="50000"
                        value={priceRange[1]}
                        onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                      />
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="px-2 py-1 bg-gray-100 rounded text-xs font-medium">
                        ${priceRange[0].toLocaleString()}
                      </span>
                      <span className="text-gray-400">—</span>
                      <span className="px-2 py-1 bg-gray-100 rounded text-xs font-medium">
                        ${priceRange[1].toLocaleString()}
                      </span>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Sizes Section */}
          <div className="border-b border-gray-100">
            <button
              onClick={() => toggleSection('sizes')}
              className="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <span className="text-lg">👟</span>
                <span className="font-semibold text-gray-900 font-poppins">Tallas</span>
              </div>
              <motion.svg
                animate={{ rotate: expandedSections.sizes ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className="w-4 h-4 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </motion.svg>
            </button>
            <AnimatePresence>
              {expandedSections.sizes && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="p-4 pt-0">
                    <div className="grid grid-cols-4 gap-2">
                      {sizes.map(size => (
                        <button
                          key={size}
                          onClick={() => handleSizeToggle(size)}
                          className={`p-2 rounded-lg border transition-all duration-300 text-center text-sm font-medium ${
                            selectedSizes.includes(size)
                              ? 'border-lime-green bg-lime-green text-black shadow-sm'
                              : 'border-gray-200 hover:border-lime-green/50 text-gray-700'
                          }`}
                        >
                          {size}
                        </button>
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Colors Section */}
          <div className="border-b border-gray-100">
            <button
              onClick={() => toggleSection('colors')}
              className="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <span className="text-lg">🎨</span>
                <span className="font-semibold text-gray-900 font-poppins">Colores</span>
              </div>
              <motion.svg
                animate={{ rotate: expandedSections.colors ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className="w-4 h-4 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </motion.svg>
            </button>
            <AnimatePresence>
              {expandedSections.colors && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="p-4 pt-0">
                    <div className="grid grid-cols-4 gap-3">
                      {colors.map(color => (
                        <button
                          key={color.id}
                          onClick={() => handleColorToggle(color.id)}
                          className={`relative p-1 rounded-lg border-2 transition-all duration-300 ${
                            selectedColors.includes(color.id)
                              ? 'border-lime-green shadow-sm'
                              : 'border-gray-200 hover:border-lime-green/50'
                          }`}
                        >
                          <div
                            className="w-8 h-8 rounded border border-gray-200"
                            style={{ backgroundColor: color.hex }}
                          />
                          {selectedColors.includes(color.id) && (
                            <div className="absolute inset-0 flex items-center justify-center">
                              <svg className="w-4 h-4 text-lime-green drop-shadow-lg" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                          )}
                        </button>
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Sort Options */}
          <div className="p-4">
            <div className="flex items-center space-x-3 mb-3">
              <span className="text-lg">📊</span>
              <span className="font-semibold text-gray-900 font-poppins">Ordenar</span>
            </div>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-green focus:border-transparent text-sm"
            >
              <option value="newest">🆕 Más Recientes</option>
              <option value="price-low">💸 Precio Menor</option>
              <option value="price-high">💎 Precio Mayor</option>
              <option value="name">🔤 Nombre A-Z</option>
              <option value="brand">🏷️ Marca A-Z</option>
              <option value="popularity">🔥 Popularidad</option>
            </select>
          </div>
        </div>
      </motion.div>
    </>
  )
}
