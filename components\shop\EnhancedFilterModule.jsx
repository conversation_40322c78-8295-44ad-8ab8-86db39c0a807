'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

export default function EnhancedFilterSidebar({
  searchQuery,
  setSearchQuery,
  selectedCategory,
  setSelectedCategory,
  selectedBrand,
  setSelectedBrand,
  sortBy,
  setSortBy,
  priceRange,
  setPriceRange,
  selectedSizes,
  setSelectedSizes,
  selectedColors,
  setSelectedColors,
  isOnSale,
  setIsOnSale,
  isLimited,
  setIsLimited,
  isNew,
  setIsNew,
  onClearAll,
  isOpen,
  setIsOpen
}) {
  const [activeFilters, setActiveFilters] = useState(0)
  const [expandedSections, setExpandedSections] = useState({
    categories: true,
    brands: false,
    price: false,
    sizes: false,
    colors: false
  })

  // Categories with icons
  const categories = [
    { id: 'all', name: 'Todos', icon: '🏪', count: 497 },
    { id: 'sneakers', name: 'Snea<PERSON>', icon: '👟', count: 245 },
    { id: 'sandals', name: '<PERSON><PERSON><PERSON>', icon: '🩴', count: 89 },
    { id: 'heels', name: '<PERSON>con<PERSON>', icon: '👠', count: 67 },
    { id: 'formal', name: 'Formales', icon: '👞', count: 96 }
  ]

  // All available brands from TWL catalog
  const brands = [
    { id: 'all', name: 'Todas', count: 497 },
    { id: 'Nike', name: 'Nike', count: 156 },
    { id: 'Adidas', name: 'Adidas', count: 134 },
    { id: 'Gucci', name: 'Gucci', count: 89 },
    { id: 'Jordan', name: 'Jordan', count: 78 },
    { id: 'Balenciaga', name: 'Balenciaga', count: 67 },
    { id: 'Off-White', name: 'Off-White', count: 45 },
    { id: 'Dior', name: 'Dior', count: 34 },
    { id: 'Louis Vuitton', name: 'Louis Vuitton', count: 28 },
    { id: 'Yeezy', name: 'Yeezy', count: 25 },
    { id: 'Golden Goose', name: 'Golden Goose', count: 22 },
    { id: 'Converse', name: 'Converse', count: 18 },
    { id: 'Vans', name: 'Vans', count: 16 },
    { id: 'Chanel', name: 'Chanel', count: 14 },
    { id: 'Hermès', name: 'Hermès', count: 12 },
    { id: 'Christian Louboutin', name: 'Louboutin', count: 11 },
    { id: 'Manolo Blahnik', name: 'Manolo Blahnik', count: 9 },
    { id: 'Tom Ford', name: 'Tom Ford', count: 8 },
    { id: 'Bottega Veneta', name: 'Bottega Veneta', count: 7 },
    { id: 'Saint Laurent', name: 'Saint Laurent', count: 6 }
  ]

  // Available sizes
  const sizes = ['35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45']

  // Available colors
  const colors = [
    { id: 'black', name: 'Negro', hex: '#000000' },
    { id: 'white', name: 'Blanco', hex: '#FFFFFF' },
    { id: 'red', name: 'Rojo', hex: '#DC2626' },
    { id: 'blue', name: 'Azul', hex: '#2563EB' },
    { id: 'green', name: 'Verde', hex: '#16A34A' },
    { id: 'yellow', name: 'Amarillo', hex: '#EAB308' },
    { id: 'pink', name: 'Rosa', hex: '#EC4899' },
    { id: 'brown', name: 'Marrón', hex: '#A16207' }
  ]

  // Toggle section expansion
  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  // Count active filters
  useEffect(() => {
    let count = 0
    if (searchQuery) count++
    if (selectedCategory !== 'all') count++
    if (selectedBrand !== 'all') count++
    if (priceRange[0] > 0 || priceRange[1] < 50000) count++
    if (selectedSizes.length > 0) count++
    if (selectedColors.length > 0) count++
    if (isOnSale) count++
    if (isLimited) count++
    if (isNew) count++
    setActiveFilters(count)
  }, [searchQuery, selectedCategory, selectedBrand, priceRange, selectedSizes, selectedColors, isOnSale, isLimited, isNew])

  const handleSizeToggle = (size) => {
    setSelectedSizes(prev => 
      prev.includes(size) 
        ? prev.filter(s => s !== size)
        : [...prev, size]
    )
  }

  const handleColorToggle = (colorId) => {
    setSelectedColors(prev => 
      prev.includes(colorId) 
        ? prev.filter(c => c !== colorId)
        : [...prev, colorId]
    )
  }

  return (
    <>
      {/* Mobile Filter Toggle */}
      <div className="lg:hidden fixed top-20 left-4 z-50">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-12 h-12 bg-lime-green hover:bg-lime-green-dark text-black rounded-full shadow-lg flex items-center justify-center transition-all duration-300"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75M9.75 12a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0m-6 0h2.25" />
          </svg>
        </button>
      </div>

      {/* Backdrop Overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setIsOpen(false)}
            style={{ top: '80px' }}
          />
        )}
      </AnimatePresence>

      {/* Filter Sidebar - Overlay Mode */}
      <motion.div
        initial={{ x: -320 }}
        animate={{ x: isOpen ? 0 : -320 }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className={`fixed top-0 left-0 h-screen w-80 bg-white shadow-2xl border-r border-gray-100 z-50 overflow-y-auto ${
          isOpen ? 'block' : 'hidden'
        }`}
        style={{ top: '80px', height: 'calc(100vh - 80px)' }}
      >
        {/* Filter Header */}
        <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-lime-green/10 to-lime-green/5">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-lime-green rounded-xl flex items-center justify-center shadow-md">
                <svg className="w-5 h-5 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75M9.75 12a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0m-6 0h2.25" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-bold text-black font-poppins">Filtros</h3>
                <p className="text-xs text-gray-500">
                  {activeFilters > 0 ? `${activeFilters} activos` : 'Personaliza tu búsqueda'}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {activeFilters > 0 && (
                <button
                  onClick={onClearAll}
                  className="px-3 py-1 text-xs text-red-600 hover:bg-red-50 rounded-lg transition-colors font-medium"
                >
                  Limpiar
                </button>
              )}
              <button
                onClick={() => setIsOpen(false)}
                className="lg:hidden p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Quick Filters */}
        <div className="p-4 border-b border-gray-100">
          <div className="grid grid-cols-1 gap-2">
            <button
              onClick={() => setIsOnSale(!isOnSale)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 text-left flex items-center space-x-2 ${
                isOnSale
                  ? 'bg-lime-green text-black shadow-sm'
                  : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 6h.008v.008H6V6z" />
              </svg>
              <span>En Oferta</span>
            </button>
            <button
              onClick={() => setIsLimited(!isLimited)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 text-left flex items-center space-x-2 ${
                isLimited
                  ? 'bg-red-500 text-white shadow-sm'
                  : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z" />
              </svg>
              <span>Edición Limitada</span>
            </button>
            <button
              onClick={() => setIsNew(!isNew)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 text-left flex items-center space-x-2 ${
                isNew
                  ? 'bg-blue-500 text-white shadow-sm'
                  : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
              </svg>
              <span>Nuevos</span>
            </button>
          </div>
        </div>

        {/* Main Filter Content */}
        <div className="flex-1 overflow-y-auto">
          {/* Search Bar */}
          <div className="p-4 border-b border-gray-100">
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Buscar productos..."
                className="w-full px-4 py-3 pl-10 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-green focus:border-transparent transition-all duration-300 text-sm"
              />
              <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
              </svg>
            </div>
          </div>

          {/* Categories Section */}
          <div className="border-b border-gray-100">
            <button
              onClick={() => toggleSection('categories')}
              className="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
                </svg>
                <span className="font-semibold text-gray-900 font-poppins">Categorías</span>
              </div>
              <motion.svg
                animate={{ rotate: expandedSections.categories ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className="w-4 h-4 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </motion.svg>
            </button>
            <AnimatePresence>
              {expandedSections.categories && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="p-4 pt-0 space-y-2">
                    {categories.map(category => (
                      <button
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                        className={`w-full p-3 rounded-lg transition-all duration-300 text-left flex items-center justify-between group ${
                          selectedCategory === category.id
                            ? 'bg-lime-green text-black shadow-sm'
                            : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <span className="text-lg">{category.icon}</span>
                          <span className="font-medium">{category.name}</span>
                        </div>
                        <span className="text-xs opacity-70">{category.count}</span>
                      </button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Brands Section */}
          <div className="border-b border-gray-100">
            <button
              onClick={() => toggleSection('brands')}
              className="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 6h.008v.008H6V6z" />
                </svg>
                <span className="font-semibold text-gray-900 font-poppins">Marcas</span>
              </div>
              <motion.svg
                animate={{ rotate: expandedSections.brands ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className="w-4 h-4 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </motion.svg>
            </button>
            <AnimatePresence>
              {expandedSections.brands && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="p-4 pt-0 space-y-2">
                    {brands.map(brand => (
                      <button
                        key={brand.id}
                        onClick={() => setSelectedBrand(brand.id)}
                        className={`w-full p-3 rounded-lg transition-all duration-300 text-left flex items-center justify-between ${
                          selectedBrand === brand.id
                            ? 'bg-lime-green text-black shadow-sm'
                            : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <span className="font-medium">{brand.name}</span>
                        <span className="text-xs opacity-70">{brand.count}</span>
                      </button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Price Range Section */}
          <div className="border-b border-gray-100">
            <button
              onClick={() => toggleSection('price')}
              className="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="font-semibold text-gray-900 font-poppins">Precio</span>
              </div>
              <motion.svg
                animate={{ rotate: expandedSections.price ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className="w-4 h-4 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </motion.svg>
            </button>
            <AnimatePresence>
              {expandedSections.price && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="p-4 pt-0 space-y-4">
                    <div className="space-y-3">
                      <input
                        type="range"
                        min="0"
                        max="50000"
                        value={priceRange[0]}
                        onChange={(e) => setPriceRange([parseInt(e.target.value), priceRange[1]])}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                      />
                      <input
                        type="range"
                        min="0"
                        max="50000"
                        value={priceRange[1]}
                        onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                      />
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="px-2 py-1 bg-gray-100 rounded text-xs font-medium">
                        ${priceRange[0].toLocaleString()}
                      </span>
                      <span className="text-gray-400">—</span>
                      <span className="px-2 py-1 bg-gray-100 rounded text-xs font-medium">
                        ${priceRange[1].toLocaleString()}
                      </span>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Sizes Section */}
          <div className="border-b border-gray-100">
            <button
              onClick={() => toggleSection('sizes')}
              className="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z" />
                </svg>
                <span className="font-semibold text-gray-900 font-poppins">Tallas</span>
              </div>
              <motion.svg
                animate={{ rotate: expandedSections.sizes ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className="w-4 h-4 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </motion.svg>
            </button>
            <AnimatePresence>
              {expandedSections.sizes && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="p-4 pt-0">
                    <div className="grid grid-cols-4 gap-2">
                      {sizes.map(size => (
                        <button
                          key={size}
                          onClick={() => handleSizeToggle(size)}
                          className={`p-2 rounded-lg border transition-all duration-300 text-center text-sm font-medium ${
                            selectedSizes.includes(size)
                              ? 'border-lime-green bg-lime-green text-black shadow-sm'
                              : 'border-gray-200 hover:border-lime-green/50 text-gray-700'
                          }`}
                        >
                          {size}
                        </button>
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Colors Section */}
          <div className="border-b border-gray-100">
            <button
              onClick={() => toggleSection('colors')}
              className="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4.098 19.902a3.75 3.75 0 005.304 0l6.401-6.402M4.098 19.902A3.75 3.75 0 109.402 4.098l6.4 6.401M4.098 19.902L9.402 4.098m6.401 6.402l-.707.707M15.803 10.5l.707-.707M9.402 4.098L15.803 10.5M9.402 4.098a3.75 3.75 0 015.304 0L15.803 5.196M15.803 10.5a3.75 3.75 0 010 5.304l-1.098 1.098" />
                </svg>
                <span className="font-semibold text-gray-900 font-poppins">Colores</span>
              </div>
              <motion.svg
                animate={{ rotate: expandedSections.colors ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className="w-4 h-4 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </motion.svg>
            </button>
            <AnimatePresence>
              {expandedSections.colors && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="p-4 pt-0">
                    <div className="grid grid-cols-4 gap-3">
                      {colors.map(color => (
                        <button
                          key={color.id}
                          onClick={() => handleColorToggle(color.id)}
                          className={`relative p-1 rounded-lg border-2 transition-all duration-300 ${
                            selectedColors.includes(color.id)
                              ? 'border-lime-green shadow-sm'
                              : 'border-gray-200 hover:border-lime-green/50'
                          }`}
                        >
                          <div
                            className="w-8 h-8 rounded border border-gray-200"
                            style={{ backgroundColor: color.hex }}
                          />
                          {selectedColors.includes(color.id) && (
                            <div className="absolute inset-0 flex items-center justify-center">
                              <svg className="w-4 h-4 text-lime-green drop-shadow-lg" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                          )}
                        </button>
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Sort Options */}
          <div className="p-4">
            <div className="flex items-center space-x-3 mb-3">
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 4.5h14.25M3 9h9.75M3 13.5h9.75m4.5-4.5v12m0 0l-3.75-3.75M17.25 21L21 17.25" />
              </svg>
              <span className="font-semibold text-gray-900 font-poppins">Ordenar</span>
            </div>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-green focus:border-transparent text-sm"
            >
              <option value="newest">Más Recientes</option>
              <option value="price-low">Precio Menor</option>
              <option value="price-high">Precio Mayor</option>
              <option value="name">Nombre A-Z</option>
              <option value="brand">Marca A-Z</option>
              <option value="popularity">Popularidad</option>
            </select>
          </div>
        </div>
      </motion.div>
    </>
  )
}
