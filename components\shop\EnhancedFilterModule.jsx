'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

export default function EnhancedFilterModule({
  searchQuery,
  setSearchQuery,
  selectedCategory,
  setSelectedCategory,
  selectedBrand,
  setSelectedBrand,
  sortBy,
  setSortBy,
  priceRange,
  setPriceRange,
  selectedSizes,
  setSelectedSizes,
  selectedColors,
  setSelectedColors,
  isOnSale,
  setIsOnSale,
  isLimited,
  setIsLimited,
  isNew,
  setIsNew,
  onClearAll
}) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [activeFilters, setActiveFilters] = useState(0)

  // Categories with icons
  const categories = [
    { id: 'all', name: 'Todos', icon: '👟', count: 497 },
    { id: 'sneakers', name: 'Sneakers', icon: '👟', count: 245 },
    { id: 'sandals', name: '<PERSON><PERSON><PERSON>', icon: '🩴', count: 89 },
    { id: 'heels', name: 'Tacon<PERSON>', icon: '👠', count: 67 },
    { id: 'formal', name: 'Formales', icon: '👞', count: 96 }
  ]

  // Luxury brands
  const brands = [
    { id: 'all', name: 'Todas las marcas', count: 497 },
    { id: 'Nike', name: 'Nike', count: 89 },
    { id: 'Adidas', name: 'Adidas', count: 67 },
    { id: 'Gucci', name: 'Gucci', count: 45 },
    { id: 'Dior', name: 'Dior', count: 34 },
    { id: '<PERSON> Vuitton', name: '<PERSON> Vuitton', count: 28 },
    { id: 'Balenciaga', name: 'Balenciaga', count: 23 },
    { id: 'Off-White', name: 'Off-White', count: 19 },
    { id: 'Golden Goose', name: 'Golden Goose', count: 15 }
  ]

  // Available sizes
  const sizes = ['35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45']

  // Available colors
  const colors = [
    { id: 'black', name: 'Negro', hex: '#000000' },
    { id: 'white', name: 'Blanco', hex: '#FFFFFF' },
    { id: 'red', name: 'Rojo', hex: '#DC2626' },
    { id: 'blue', name: 'Azul', hex: '#2563EB' },
    { id: 'green', name: 'Verde', hex: '#16A34A' },
    { id: 'yellow', name: 'Amarillo', hex: '#EAB308' },
    { id: 'pink', name: 'Rosa', hex: '#EC4899' },
    { id: 'brown', name: 'Marrón', hex: '#A16207' }
  ]

  // Count active filters
  useEffect(() => {
    let count = 0
    if (searchQuery) count++
    if (selectedCategory !== 'all') count++
    if (selectedBrand !== 'all') count++
    if (priceRange[0] > 0 || priceRange[1] < 50000) count++
    if (selectedSizes.length > 0) count++
    if (selectedColors.length > 0) count++
    if (isOnSale) count++
    if (isLimited) count++
    if (isNew) count++
    setActiveFilters(count)
  }, [searchQuery, selectedCategory, selectedBrand, priceRange, selectedSizes, selectedColors, isOnSale, isLimited, isNew])

  const handleSizeToggle = (size) => {
    setSelectedSizes(prev => 
      prev.includes(size) 
        ? prev.filter(s => s !== size)
        : [...prev, size]
    )
  }

  const handleColorToggle = (colorId) => {
    setSelectedColors(prev => 
      prev.includes(colorId) 
        ? prev.filter(c => c !== colorId)
        : [...prev, colorId]
    )
  }

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
      {/* Filter Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-lime-green rounded-xl flex items-center justify-center">
              <svg className="w-5 h-5 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
              </svg>
            </div>
            <div>
              <h3 className="text-xl font-bold text-black font-poppins">Filtros Avanzados</h3>
              <p className="text-sm text-gray-500">
                {activeFilters > 0 ? `${activeFilters} filtros activos` : 'Personaliza tu búsqueda'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {activeFilters > 0 && (
              <button
                onClick={onClearAll}
                className="px-4 py-2 text-sm text-gray-600 hover:text-red-600 transition-colors font-medium"
              >
                Limpiar Todo
              </button>
            )}
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <motion.svg 
                className="w-5 h-5 text-gray-600"
                animate={{ rotate: isExpanded ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </motion.svg>
            </button>
          </div>
        </div>
      </div>

      {/* Quick Filters Bar */}
      <div className="p-4 bg-gray-50 border-b border-gray-100">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setIsOnSale(!isOnSale)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
              isOnSale 
                ? 'bg-lime-green text-black shadow-md' 
                : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'
            }`}
          >
            🏷️ En Oferta
          </button>
          <button
            onClick={() => setIsLimited(!isLimited)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
              isLimited 
                ? 'bg-red-500 text-white shadow-md' 
                : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'
            }`}
          >
            ⭐ Edición Limitada
          </button>
          <button
            onClick={() => setIsNew(!isNew)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
              isNew 
                ? 'bg-blue-500 text-white shadow-md' 
                : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'
            }`}
          >
            ✨ Nuevos
          </button>
        </div>
      </div>

      {/* Expanded Filters */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="overflow-hidden"
          >
            <div className="p-6 space-y-8">
              {/* Search Bar */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3 font-poppins">
                  🔍 Buscar Productos
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Buscar por nombre, marca o descripción..."
                    className="w-full px-4 py-3 pl-12 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-lime-green focus:border-transparent transition-all duration-300 font-inter"
                  />
                  <svg className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>

              {/* Categories Grid */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3 font-poppins">
                  📂 Categorías
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
                  {categories.map(category => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`p-4 rounded-xl border-2 transition-all duration-300 text-center group ${
                        selectedCategory === category.id
                          ? 'border-lime-green bg-lime-green/10 shadow-md'
                          : 'border-gray-200 hover:border-lime-green/50 hover:bg-gray-50'
                      }`}
                    >
                      <div className="text-2xl mb-2">{category.icon}</div>
                      <div className="text-sm font-medium text-gray-900 font-poppins">{category.name}</div>
                      <div className="text-xs text-gray-500 mt-1">{category.count} productos</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Brands Selection */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3 font-poppins">
                  🏷️ Marcas de Lujo
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {brands.map(brand => (
                    <button
                      key={brand.id}
                      onClick={() => setSelectedBrand(brand.id)}
                      className={`p-3 rounded-lg border transition-all duration-300 text-left ${
                        selectedBrand === brand.id
                          ? 'border-lime-green bg-lime-green/10 text-black'
                          : 'border-gray-200 hover:border-lime-green/50 text-gray-700'
                      }`}
                    >
                      <div className="font-medium font-poppins">{brand.name}</div>
                      <div className="text-xs text-gray-500">{brand.count} productos</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Price Range */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3 font-poppins">
                  💰 Rango de Precio
                </label>
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex-1">
                      <input
                        type="range"
                        min="0"
                        max="50000"
                        value={priceRange[0]}
                        onChange={(e) => setPriceRange([parseInt(e.target.value), priceRange[1]])}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                      />
                    </div>
                    <div className="flex-1">
                      <input
                        type="range"
                        min="0"
                        max="50000"
                        value={priceRange[1]}
                        onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                      />
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="px-3 py-2 bg-gray-100 rounded-lg font-medium">
                      ${priceRange[0].toLocaleString()} MXN
                    </span>
                    <span className="text-gray-400">—</span>
                    <span className="px-3 py-2 bg-gray-100 rounded-lg font-medium">
                      ${priceRange[1].toLocaleString()} MXN
                    </span>
                  </div>
                </div>
              </div>

              {/* Sizes */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3 font-poppins">
                  👟 Tallas Disponibles
                </label>
                <div className="grid grid-cols-6 md:grid-cols-11 gap-2">
                  {sizes.map(size => (
                    <button
                      key={size}
                      onClick={() => handleSizeToggle(size)}
                      className={`p-3 rounded-lg border-2 transition-all duration-300 text-center font-medium ${
                        selectedSizes.includes(size)
                          ? 'border-lime-green bg-lime-green text-black shadow-md'
                          : 'border-gray-200 hover:border-lime-green/50 text-gray-700'
                      }`}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>

              {/* Colors */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3 font-poppins">
                  🎨 Colores
                </label>
                <div className="grid grid-cols-4 md:grid-cols-8 gap-3">
                  {colors.map(color => (
                    <button
                      key={color.id}
                      onClick={() => handleColorToggle(color.id)}
                      className={`relative p-1 rounded-xl border-2 transition-all duration-300 ${
                        selectedColors.includes(color.id)
                          ? 'border-lime-green shadow-md'
                          : 'border-gray-200 hover:border-lime-green/50'
                      }`}
                    >
                      <div
                        className="w-12 h-12 rounded-lg border border-gray-200"
                        style={{ backgroundColor: color.hex }}
                      />
                      {selectedColors.includes(color.id) && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <svg className="w-6 h-6 text-lime-green drop-shadow-lg" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                      <div className="text-xs text-center mt-1 font-medium text-gray-600">{color.name}</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Sort Options */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3 font-poppins">
                  📊 Ordenar Por
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {[
                    { value: 'newest', label: '🆕 Más Recientes', desc: 'Últimos lanzamientos' },
                    { value: 'price-low', label: '💸 Precio Menor', desc: 'De menor a mayor' },
                    { value: 'price-high', label: '💎 Precio Mayor', desc: 'De mayor a menor' },
                    { value: 'name', label: '🔤 Nombre A-Z', desc: 'Orden alfabético' },
                    { value: 'brand', label: '🏷️ Marca A-Z', desc: 'Por marca' },
                    { value: 'popularity', label: '🔥 Popularidad', desc: 'Más vendidos' }
                  ].map(option => (
                    <button
                      key={option.value}
                      onClick={() => setSortBy(option.value)}
                      className={`p-4 rounded-xl border-2 transition-all duration-300 text-left ${
                        sortBy === option.value
                          ? 'border-lime-green bg-lime-green/10 shadow-md'
                          : 'border-gray-200 hover:border-lime-green/50 hover:bg-gray-50'
                      }`}
                    >
                      <div className="font-medium text-gray-900 font-poppins">{option.label}</div>
                      <div className="text-xs text-gray-500 mt-1">{option.desc}</div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
