'use client'

import { useState, useEffect, useMemo } from 'react'
import { useSimpleCart, useSimpleWishlist } from '../../components/providers/TWLProviders'
import OptimizedProductCard from '../../components/performance/OptimizedProductCard'
import BackToTop from '../../components/ui/BackToTop'
import { ProductCardSkeletons } from '../../components/ui/ProductCardSkeleton'
import { useInfiniteScroll } from '../../hooks/useInfiniteScroll'
import EnhancedFilterSidebar from '../../components/shop/EnhancedFilterModule'
import { useAdvancedFilters } from '../../hooks/useAdvancedFilters'



export default function ShopPage() {
  const [products, setProducts] = useState([])
  const [isFilterOpen, setIsFilterOpen] = useState(false) // Filters hidden by default, overlay when opened

  // Mock products data
  useEffect(() => {
    const mockProducts = [
      { id: 1, name: 'Nike Air Force 1 x Gucci', brand: 'Nike', category: 'sneakers', price: 4500, originalPrice: 5200, isLimited: true, isNew: false, description: 'Colaboración exclusiva entre Nike y Gucci', sizes: ['38', '39', '40', '41', '42'], colors: ['white', 'black'], popularity: 95, image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop', hoverImage: 'https://images.unsplash.com/photo-1600185365483-26d7a4cc7519?w=400&h=400&fit=crop' },
      { id: 2, name: 'Dior B23 High-Top', brand: 'Dior', category: 'sneakers', price: 22000, originalPrice: 26400, isLimited: false, isNew: true, description: 'Sneakers de lujo con diseño oblique', sizes: ['37', '38', '39', '40', '41'], colors: ['white', 'blue'], popularity: 88, image: 'https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2?w=400&h=400&fit=crop', hoverImage: 'https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?w=400&h=400&fit=crop' },
      { id: 3, name: 'Louis Vuitton Trainer', brand: 'Louis Vuitton', category: 'sneakers', price: 28500, originalPrice: 34200, isLimited: false, isNew: true, description: 'Trainer de lujo con monograma LV', sizes: ['38', '39', '40', '41', '42', '43'], colors: ['brown', 'black'], popularity: 92, image: 'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=400&h=400&fit=crop', hoverImage: 'https://images.unsplash.com/photo-1584735175315-9d5df23860e6?w=400&h=400&fit=crop' },
      { id: 4, name: 'Gucci Ace Sneaker', brand: 'Gucci', category: 'sneakers', price: 12800, originalPrice: 15360, description: 'Sneaker clásico con detalles de abeja', image: 'https://images.unsplash.com/photo-1560769629-975ec94e6a86?w=400&h=400&fit=crop', hoverImage: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop' },
      { id: 5, name: 'Yeezy Boost 350 V2', brand: 'Adidas', category: 'sneakers', price: 4800, originalPrice: 5760, isLimited: true, description: 'Diseño icónico de Kanye West', image: 'https://images.unsplash.com/photo-1552346154-21d32810aba3?w=400&h=400&fit=crop', hoverImage: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop' },
      { id: 6, name: 'Golden Goose Superstar', brand: 'Golden Goose', category: 'sneakers', price: 8900, originalPrice: 10680, description: 'Sneakers con acabado vintage', image: 'https://images.unsplash.com/photo-1525966222134-fcfa99b8ae77?w=400&h=400&fit=crop', hoverImage: 'https://images.unsplash.com/photo-1514989940723-e8e51635b782?w=400&h=400&fit=crop' },
      { id: 7, name: 'Balenciaga Triple S', brand: 'Balenciaga', category: 'sneakers', price: 15600, originalPrice: 18720, isExclusive: true, description: 'Chunky sneaker de lujo', image: 'https://images.unsplash.com/photo-1539185441755-769473a23570?w=400&h=400&fit=crop', hoverImage: 'https://images.unsplash.com/photo-1574680096145-d05b474e2155?w=400&h=400&fit=crop' },
      { id: 8, name: 'Off-White Dunk Low', brand: 'Nike', category: 'sneakers', price: 6200, originalPrice: 7440, isLimited: true, description: 'Colaboración con Virgil Abloh', image: 'https://images.unsplash.com/photo-1600269452121-4f2416e55c28?w=400&h=400&fit=crop', hoverImage: 'https://images.unsplash.com/photo-1556906781-9a412961c28c?w=400&h=400&fit=crop' },
      { id: 9, name: 'Hermès Oran Sandals', brand: 'Hermès', category: 'sandals', price: 18500, originalPrice: 22200, description: 'Sandalias de lujo en cuero', image: 'https://images.unsplash.com/photo-1603808033192-082d6919d3e1?w=400&h=400&fit=crop', hoverImage: 'https://images.unsplash.com/photo-1594223274512-ad4803739b7c?w=400&h=400&fit=crop' },
      { id: 10, name: 'Louboutin Pigalle', brand: 'Christian Louboutin', category: 'heels', price: 16800, originalPrice: 20160, description: 'Tacones icónicos con suela roja', image: 'https://images.unsplash.com/photo-1543163521-1bf539c55dd2?w=400&h=400&fit=crop', hoverImage: 'https://images.unsplash.com/photo-1535043934128-cf0b28d52f95?w=400&h=400&fit=crop' },
      { id: 11, name: 'Manolo Blahnik Hangisi', brand: 'Manolo Blahnik', category: 'heels', price: 19200, originalPrice: 23040, description: 'Tacones con cristal Swarovski', image: 'https://images.unsplash.com/photo-1596702962347-cbbe9411b951?w=400&h=400&fit=crop', hoverImage: 'https://images.unsplash.com/photo-1581101767113-1677fc2beaa8?w=400&h=400&fit=crop' },
      { id: 12, name: 'Tom Ford Oxford', brand: 'Tom Ford', category: 'formal', price: 24500, originalPrice: 29400, description: 'Zapatos formales de lujo', image: 'https://images.unsplash.com/photo-1582897085656-c636d006a246?w=400&h=400&fit=crop', hoverImage: 'https://images.unsplash.com/photo-1614252369475-531eba835eb1?w=400&h=400&fit=crop' }
    ]
    setProducts(mockProducts)
  }, [])

  // Use advanced filters hook
  const {
    searchQuery,
    setSearchQuery,
    selectedCategory,
    setSelectedCategory,
    selectedBrand,
    setSelectedBrand,
    sortBy,
    setSortBy,
    priceRange,
    setPriceRange,
    selectedSizes,
    setSelectedSizes,
    selectedColors,
    setSelectedColors,
    isOnSale,
    setIsOnSale,
    isLimited,
    setIsLimited,
    isNew,
    setIsNew,
    filteredProducts,
    filterStats,
    activeFilters,
    clearAllFilters
  } = useAdvancedFilters(products)

  // Filtering logic now handled by useAdvancedFilters hook

  // Infinite scroll implementation
  const {
    displayedItems: displayedProducts,
    hasMore,
    isLoading: isLoadingMore,
    reset: resetInfiniteScroll
  } = useInfiniteScroll(filteredProducts, 12) // Load 12 products at a time

  // Reset infinite scroll when filters change
  useEffect(() => {
    resetInfiniteScroll()
  }, [filteredProducts, resetInfiniteScroll])

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Filter Sidebar */}
      <EnhancedFilterSidebar
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        selectedCategory={selectedCategory}
        setSelectedCategory={setSelectedCategory}
        selectedBrand={selectedBrand}
        setSelectedBrand={setSelectedBrand}
        sortBy={sortBy}
        setSortBy={setSortBy}
        priceRange={priceRange}
        setPriceRange={setPriceRange}
        selectedSizes={selectedSizes}
        setSelectedSizes={setSelectedSizes}
        selectedColors={selectedColors}
        setSelectedColors={setSelectedColors}
        isOnSale={isOnSale}
        setIsOnSale={setIsOnSale}
        isLimited={isLimited}
        setIsLimited={setIsLimited}
        isNew={isNew}
        setIsNew={setIsNew}
        onClearAll={clearAllFilters}
        isOpen={isFilterOpen}
        setIsOpen={setIsFilterOpen}
      />

      {/* Main Content - Fixed Layout */}
      <div className="w-full">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-4xl font-bold text-black mb-2 font-godber tracking-wide">
                    Tienda TWL
                  </h1>
                  <p className="text-lg text-gray-600">
                    Descubre nuestra colección exclusiva de calzado de lujo
                  </p>
                </div>

                {/* Mobile Filter Toggle */}
                <button
                  onClick={() => setIsFilterOpen(true)}
                  className="lg:hidden w-12 h-12 bg-lime-green hover:bg-lime-green-dark text-black rounded-full shadow-lg flex items-center justify-center transition-all duration-300"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Results count and filter summary */}
            <div className="mb-6 flex items-center justify-between">
              <div>
                <p className="text-gray-600 text-sm">
                  Mostrando <span className="font-semibold text-black">{displayedProducts.length}</span> de <span className="font-semibold text-black">{filteredProducts.length}</span> productos
                </p>
                {activeFilters > 0 && (
                  <p className="text-xs text-lime-600 mt-1">
                    {activeFilters} filtro{activeFilters > 1 ? 's' : ''} aplicado{activeFilters > 1 ? 's' : ''}
                  </p>
                )}
              </div>

              {/* Desktop Filter Toggle */}
              <button
                onClick={() => setIsFilterOpen(!isFilterOpen)}
                className="hidden lg:flex items-center space-x-2 px-4 py-2 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                <span className="text-sm font-medium text-gray-700">
                  {isFilterOpen ? 'Ocultar Filtros' : 'Mostrar Filtros'}
                </span>
              </button>
            </div>

            {/* Products Grid - Always Full Width */}
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-5 gap-6">
              {displayedProducts.map((product, index) => (
                <OptimizedProductCard
                  key={product.id}
                  product={product}
                  priority={index < 8} // Prioritize first 8 products for faster loading
                />
              ))}

              {/* Loading skeletons for infinite scroll */}
              {isLoadingMore && <ProductCardSkeletons count={8} />}
            </div>

            {/* Infinite scroll sentinel */}
            {hasMore && (
              <div id="scroll-sentinel" className="h-10 flex items-center justify-center mt-8">
                <div className="text-gray-500 text-sm">
                  {isLoadingMore ? 'Cargando más productos...' : 'Desplázate para cargar más'}
                </div>
              </div>
            )}

            {/* End of results message */}
            {!hasMore && displayedProducts.length > 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500">
                  Has visto todos los productos disponibles
                </p>
              </div>
            )}

            {/* No results */}
            {filteredProducts.length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">👟</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No se encontraron productos
                </h3>
                <p className="text-gray-600 mb-4">
                  Intenta ajustar tus filtros o buscar algo diferente
                </p>
                <button
                  onClick={clearAllFilters}
                  className="px-6 py-3 bg-lime-500 hover:bg-lime-600 text-black rounded-lg font-medium transition-colors"
                >
                  Limpiar Filtros
                </button>
              </div>
            )}
        </div>
      </div>

      {/* Back to Top Button */}
      <BackToTop />
    </div>
  )
}
