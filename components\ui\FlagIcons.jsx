// Round SVG Flag Icons for TWL Navigation
// Professional, consistent flag icons that match TWL's aesthetic

export const MexicoFlag = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    className={`rounded-full ${className}`}
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="12" cy="12" r="12" fill="#fff" />
    <defs>
      <clipPath id="mexico-clip">
        <circle cx="12" cy="12" r="12" />
      </clipPath>
    </defs>
    <g clipPath="url(#mexico-clip)">
      {/* Green stripe */}
      <rect x="0" y="0" width="8" height="24" fill="#006847" />
      {/* White stripe with coat of arms area */}
      <rect x="8" y="0" width="8" height="24" fill="#FFFFFF" />
      {/* Red stripe */}
      <rect x="16" y="0" width="8" height="24" fill="#CE1126" />
      {/* Simplified coat of arms */}
      <circle cx="12" cy="12" r="3" fill="#8B4513" opacity="0.8" />
      <circle cx="12" cy="11" r="1.5" fill="#FFD700" />
    </g>
  </svg>
)

export const USAFlag = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    className={`rounded-full ${className}`}
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="12" cy="12" r="12" fill="#fff" />
    <defs>
      <clipPath id="usa-clip">
        <circle cx="12" cy="12" r="12" />
      </clipPath>
    </defs>
    <g clipPath="url(#usa-clip)">
      {/* Red stripes */}
      <rect x="0" y="0" width="24" height="24" fill="#B22234" />
      {/* White stripes */}
      <rect x="0" y="1.8" width="24" height="1.8" fill="#FFFFFF" />
      <rect x="0" y="5.4" width="24" height="1.8" fill="#FFFFFF" />
      <rect x="0" y="9" width="24" height="1.8" fill="#FFFFFF" />
      <rect x="0" y="12.6" width="24" height="1.8" fill="#FFFFFF" />
      <rect x="0" y="16.2" width="24" height="1.8" fill="#FFFFFF" />
      <rect x="0" y="19.8" width="24" height="1.8" fill="#FFFFFF" />
      {/* Blue canton */}
      <rect x="0" y="0" width="9.6" height="12.6" fill="#3C3B6E" />
      {/* Simplified stars */}
      <circle cx="2.4" cy="2.1" r="0.4" fill="#FFFFFF" />
      <circle cx="4.8" cy="2.1" r="0.4" fill="#FFFFFF" />
      <circle cx="7.2" cy="2.1" r="0.4" fill="#FFFFFF" />
      <circle cx="3.6" cy="4.2" r="0.4" fill="#FFFFFF" />
      <circle cx="6" cy="4.2" r="0.4" fill="#FFFFFF" />
      <circle cx="2.4" cy="6.3" r="0.4" fill="#FFFFFF" />
      <circle cx="4.8" cy="6.3" r="0.4" fill="#FFFFFF" />
      <circle cx="7.2" cy="6.3" r="0.4" fill="#FFFFFF" />
      <circle cx="3.6" cy="8.4" r="0.4" fill="#FFFFFF" />
      <circle cx="6" cy="8.4" r="0.4" fill="#FFFFFF" />
      <circle cx="2.4" cy="10.5" r="0.4" fill="#FFFFFF" />
      <circle cx="4.8" cy="10.5" r="0.4" fill="#FFFFFF" />
      <circle cx="7.2" cy="10.5" r="0.4" fill="#FFFFFF" />
    </g>
  </svg>
)

export const BrazilFlag = ({ size = 24, className = "" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    className={`rounded-full ${className}`}
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="12" cy="12" r="12" fill="#fff" />
    <defs>
      <clipPath id="brazil-clip">
        <circle cx="12" cy="12" r="12" />
      </clipPath>
    </defs>
    <g clipPath="url(#brazil-clip)">
      {/* Green background */}
      <rect x="0" y="0" width="24" height="24" fill="#009739" />
      {/* Yellow diamond */}
      <path d="M12 3 L21 12 L12 21 L3 12 Z" fill="#FEDD00" />
      {/* Blue circle */}
      <circle cx="12" cy="12" r="4" fill="#012169" />
      {/* Simplified banner */}
      <ellipse cx="12" cy="12" rx="3.5" ry="1" fill="#FFFFFF" opacity="0.9" />
    </g>
  </svg>
)

// Flag icon mapping for easy use
export const FlagIcons = {
  'es-MX': MexicoFlag,
  'en-US': USAFlag,
  'pt-BR': BrazilFlag
}
