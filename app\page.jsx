'use client'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative min-h-screen bg-gradient-to-br from-white via-gray-50 to-gray-100 flex items-center justify-center overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, #BFFF00 0%, transparent 50%),
                             radial-gradient(circle at 75% 75%, #000000 0%, transparent 50%)`
          }} />
        </div>

        {/* Content */}
        <div className="relative z-10 text-center px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            {/* Logo/Brand */}
            <div className="mb-8">
              <h1 className="text-6xl md:text-8xl font-bold text-gray-900 mb-4">
                <span className="text-black">THE</span>
                <span className="text-lime-400 ml-4">WHITE</span>
                <span className="text-black ml-4">LACES</span>
              </h1>
              <p className="text-xl md:text-2xl text-gray-600 font-light">
                Luxury Streetwear Footwear
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="/shop" className="bg-lime-400 hover:bg-lime-500 text-black px-8 py-4 rounded-lg text-lg font-semibold transition-colors">
                Explorar Colección
              </a>
              <a href="/limited-editions" className="border-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors">
                Ver Drops Exclusivos
              </a>
            </div>

            {/* Stats */}
            <div className="mt-16 grid grid-cols-3 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-gray-900">500+</div>
                <div className="text-gray-600">Productos Premium</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-gray-900">50+</div>
                <div className="text-gray-600">Marcas Exclusivas</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-gray-900">24h</div>
                <div className="text-gray-600">Envío Express</div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
          <div className="animate-bounce">
            <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </div>
        </div>
      </section>

      {/* Shop The Look Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Shop The Look
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Inspiración directa del streetwear urbano. Descubre cómo combinar nuestros productos.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Lifestyle Image 1 */}
            <div className="relative h-96 bg-gray-200 rounded-lg overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div className="absolute bottom-4 left-4 text-white">
                <p className="text-sm font-medium">Street Style</p>
                <p className="text-xs opacity-80">Urban Essentials</p>
              </div>
            </div>

            {/* Lifestyle Image 2 */}
            <div className="relative h-96 bg-gray-200 rounded-lg overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              <div className="absolute bottom-4 left-4 text-white">
                <p className="text-sm font-medium">Luxury Casual</p>
                <p className="text-xs opacity-80">Premium Comfort</p>
              </div>
            </div>

            {/* Product Card 1 */}
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="h-64 bg-gray-200"></div>
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-2">Nike Air Force Premium</h3>
                <p className="text-sm text-gray-600 mb-3">Edición limitada streetwear</p>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-lime-600">$3,570 MXN</span>
                  <button className="bg-lime-500 hover:bg-lime-600 text-black px-3 py-1 rounded-lg text-sm transition-colors">
                    Ver
                  </button>
                </div>
              </div>
            </div>

            {/* Product Card 2 */}
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="h-64 bg-gray-200"></div>
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-2">Gucci Sneakers Luxury</h3>
                <p className="text-sm text-gray-600 mb-3">Colaboración exclusiva</p>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-lime-600">$8,950 MXN</span>
                  <button className="bg-lime-500 hover:bg-lime-600 text-black px-3 py-1 rounded-lg text-sm transition-colors">
                    Ver
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Nuestra Colección Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Nuestra Colección
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Explora nuestras categorías premium de calzado de lujo y streetwear exclusivo.
            </p>
          </div>

          {/* Category Buttons */}
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            {['Sneakers', 'Casual', 'Tacones', 'Sandalias', 'Formal'].map((category) => (
              <button
                key={category}
                className="px-6 py-3 bg-white border-2 border-gray-300 text-gray-700 rounded-lg hover:border-lime-500 hover:text-lime-600 transition-colors"
              >
                {category}
              </button>
            ))}
          </div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
              <div key={item} className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="h-48 bg-gray-200"></div>
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">Producto {item}</h3>
                  <p className="text-sm text-gray-600 mb-3">Descripción premium</p>
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-bold text-lime-600">$3,570 MXN</span>
                    <button className="bg-lime-500 hover:bg-lime-600 text-black px-3 py-1 rounded-lg text-sm transition-colors">
                      Ver
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Ver toda la colección button */}
          <div className="text-center">
            <button className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-3 rounded-lg transition-colors">
              Ver toda la colección
            </button>
          </div>
        </div>
      </section>

      {/* Hottest Drops Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-900">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Hottest Drops
            </h2>
            <p className="text-lg text-gray-300 max-w-2xl mx-auto">
              Los lanzamientos más esperados y exclusivos. Ediciones limitadas que no puedes perderte.
            </p>
          </div>

          {/* Automatic Slider */}
          <div className="flex justify-center">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 max-w-6xl">
              {[1, 2, 3, 4, 5].map((item) => (
                <div key={item} className="bg-white rounded-lg shadow-lg overflow-hidden">
                  <div className="h-48 bg-gray-200 relative">
                    <div className="absolute top-2 left-2">
                      <span className="px-2 py-1 bg-red-500 text-white text-xs font-medium rounded">
                        LIMITADO
                      </span>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-2">Drop {item}</h3>
                    <p className="text-sm text-gray-600 mb-3">Edición exclusiva</p>
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-lime-600">$4,570 MXN</span>
                      <button className="bg-lime-500 hover:bg-lime-600 text-black px-3 py-1 rounded-lg text-sm transition-colors">
                        Ver
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-900">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Mantente al Día
          </h2>
          <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">
            Sé el primero en conocer sobre drops exclusivos, colaboraciones limitadas y acceso temprano a nuestras colecciones.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="<EMAIL>"
              className="flex-1 px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-lime-500"
            />
            <button className="bg-lime-500 hover:bg-lime-600 text-black font-medium px-6 py-3 rounded-lg transition-colors whitespace-nowrap">
              Suscribirse
            </button>
          </div>
        </div>
      </section>
    </div>
  )
}


