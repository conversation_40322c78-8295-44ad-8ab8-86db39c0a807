'use client'

import { Suspense } from 'react'
import { useSimpleCart, useSimpleWishlist } from '../components/providers/TWLProviders'

// Interactive Product Card Component
function InteractiveProductCard({ product, index }) {
  const { addItem } = useSimpleCart()
  const { toggleWishlist, isInWishlist } = useSimpleWishlist()

  const handleAddToCart = () => {
    addItem(product, 'M', 1)
  }

  const handleToggleWishlist = () => {
    toggleWishlist(product)
  }

  const isWishlisted = isInWishlist(product.id)

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
      <div className="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
        {/* Product badges */}
        <div className="absolute top-3 left-3 z-10">
          {product.isLimited && (
            <span className="px-2 py-1 bg-red-500 text-white text-xs font-medium rounded-full">
              LIMITADO
            </span>
          )}
          {product.isExclusive && (
            <span className="px-2 py-1 bg-purple-500 text-white text-xs font-medium rounded-full">
              EXCLUSIVO
            </span>
          )}
          {product.isVip && (
            <span className="px-2 py-1 bg-yellow-500 text-black text-xs font-medium rounded-full">
              VIP
            </span>
          )}
          {product.isNew && (
            <span className="px-2 py-1 bg-green-500 text-white text-xs font-medium rounded-full">
              NUEVO
            </span>
          )}
        </div>

        {/* Wishlist button */}
        <button
          onClick={handleToggleWishlist}
          className={`absolute top-3 right-3 p-2 backdrop-blur-sm rounded-full hover:bg-white transition-all duration-300 ${
            isWishlisted ? 'bg-red-100 text-red-500' : 'bg-white/80 text-gray-600'
          }`}
        >
          <svg className="w-4 h-4" fill={isWishlisted ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button>

        {/* Quick view overlay */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <button className="px-4 py-2 bg-white text-black rounded-lg font-medium hover:bg-gray-100 transition-colors">
            Vista Rápida
          </button>
        </div>
      </div>

      <div className="p-4">
        <div className="mb-2">
          <p className="text-xs text-gray-500 uppercase tracking-wide">{product.brand}</p>
          <h3 className="font-semibold text-gray-900 text-sm leading-tight">{product.name}</h3>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex flex-col">
            <span className="text-lg font-bold text-lime-600">${product.price.toLocaleString()} MXN</span>
            {product.originalPrice && (
              <span className="text-sm text-gray-400 line-through">${product.originalPrice.toLocaleString()} MXN</span>
            )}
          </div>
          <button
            onClick={handleAddToCart}
            className="w-8 h-8 bg-lime-500 hover:bg-lime-600 text-black rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  )
}

// Hero Component (inline for now)
function Hero() {
  return (
    <section className="relative min-h-screen bg-gradient-to-br from-white via-gray-50 to-gray-100 flex items-center justify-center overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, #BFFF00 0%, transparent 50%),
                           radial-gradient(circle at 75% 75%, #000000 0%, transparent 50%)`
        }} />
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-32 h-32 bg-lime-500/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-black/5 rounded-full blur-xl"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-lime-500/5 rounded-full blur-lg"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="space-y-8">
          {/* Badge */}
          <div className="inline-flex">
            <div className="bg-white/90 backdrop-blur-md px-6 py-3 text-sm font-medium text-lime-600 border border-lime-200/50 rounded-full shadow-lg">
              ✨ Descubre calzado streetwear de lujo en The White Laces
            </div>
          </div>

          {/* Main Title */}
          <div className="space-y-4">
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-black px-4">
              <span className="block">THE</span>
              <span className="block text-lime-500">WHITE</span>
              <span className="block">LACES</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 font-light">
              Luxury Streetwear Footwear
            </p>
          </div>

          {/* Description */}
          <p className="text-base sm:text-lg md:text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed px-4">
            Sneakers premium, ediciones limitadas y drops exclusivos de las mejores marcas. México primero, LATAM listo.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center pt-8 px-4">
            <button className="w-full sm:w-auto min-w-[200px] bg-lime-500 hover:bg-lime-600 text-black font-medium py-4 px-8 rounded-xl transition-all duration-300 hover:shadow-lg hover:shadow-lime-500/25 hover:scale-105">
              Explorar Colección
            </button>
            <button className="w-full sm:w-auto min-w-[200px] bg-black/90 backdrop-blur-sm hover:bg-black text-white font-medium py-4 px-8 rounded-xl transition-all duration-300 hover:shadow-lg hover:scale-105">
              Ver Drops Limitados
            </button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 pt-12 sm:pt-16 max-w-4xl mx-auto px-4">
            {[
              { value: "500+", label: "Productos Premium" },
              { value: "50+", label: "Marcas Exclusivas" },
              { value: "24h", label: "Envío Express" }
            ].map((stat, index) => (
              <div key={index} className="text-center p-6 bg-white/60 backdrop-blur-md rounded-xl border border-white/20 shadow-lg">
                <div className="text-3xl sm:text-4xl font-bold text-lime-600 mb-2">
                  {stat.value}
                </div>
                <div className="text-sm text-gray-600 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="animate-bounce">
          <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>
    </section>
  )
}

export default function HomePage() {
  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine">
      {/* Hero Section */}
      <Hero />

      {/* Shop The Look Section */}
      <section className="relative py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 to-white overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-10 right-10 w-64 h-64 bg-lime-500/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-10 left-10 w-48 h-48 bg-black/5 rounded-full blur-2xl"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-black mb-4">
              Shop The Look
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Inspiración directa del streetwear urbano. Descubre cómo combinar nuestros productos.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Lifestyle Image 1 */}
            <div className="relative h-96 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 rounded-xl overflow-hidden glass-card">
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
              <div className="absolute bottom-6 left-6 text-white">
                <p className="text-sm font-medium font-poppins">Street Style</p>
                <p className="text-xs opacity-80 font-poppins">Urban Essentials</p>
              </div>
            </div>

            {/* Lifestyle Image 2 */}
            <div className="relative h-96 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 rounded-xl overflow-hidden glass-card">
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
              <div className="absolute bottom-6 left-6 text-white">
                <p className="text-sm font-medium font-poppins">Luxury Casual</p>
                <p className="text-xs opacity-80 font-poppins">Premium Comfort</p>
              </div>
            </div>

            {/* Product Card 1 */}
            <div className="bg-pure-white dark:bg-mist-gray rounded-xl shadow-lg overflow-hidden glass-card hover:shadow-xl transition-all duration-300">
              <div className="h-64 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-600 dark:to-gray-700"></div>
              <div className="p-4">
                <h3 className="font-semibold text-pure-black dark:text-pure-white mb-2 font-poppins">Nike Air Force Premium</h3>
                <p className="text-sm text-text-gray mb-3 font-poppins">Edición limitada streetwear</p>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-lime-green font-poppins">$3,570 MXN</span>
                  <button className="bg-lime-green hover:bg-lime-green/90 text-pure-black px-3 py-1 rounded-lg text-sm transition-colors font-poppins font-medium">
                    Ver
                  </button>
                </div>
              </div>
            </div>

            {/* Product Card 2 */}
            <div className="bg-pure-white dark:bg-mist-gray rounded-xl shadow-lg overflow-hidden glass-card hover:shadow-xl transition-all duration-300">
              <div className="h-64 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-600 dark:to-gray-700"></div>
              <div className="p-4">
                <h3 className="font-semibold text-pure-black dark:text-pure-white mb-2 font-poppins">Gucci Sneakers Luxury</h3>
                <p className="text-sm text-text-gray mb-3 font-poppins">Colaboración exclusiva</p>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-lime-green font-poppins">$8,950 MXN</span>
                  <button className="bg-lime-green hover:bg-lime-green/90 text-pure-black px-3 py-1 rounded-lg text-sm transition-colors font-poppins font-medium">
                    Ver
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Nuestra Colección Section */}
      <section className="relative py-16 px-4 sm:px-6 lg:px-8 bg-white overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `repeating-linear-gradient(45deg, transparent, transparent 35px, #BFFF00 35px, #BFFF00 70px)`
          }} />
        </div>

        <div className="relative z-10 max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-black mb-4">
              Nuestra Colección
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Explora nuestras categorías premium de calzado de lujo y streetwear exclusivo.
            </p>
          </div>

          {/* Category Buttons */}
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            {['Sneakers', 'Casual', 'Tacones', 'Sandalias', 'Formal'].map((category) => (
              <button
                key={category}
                className="px-6 py-3 bg-white border-2 border-gray-300 text-gray-700 rounded-lg hover:border-lime-500 hover:text-lime-600 transition-colors font-medium"
              >
                {category}
              </button>
            ))}
          </div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[
              { id: 1, name: 'Nike x Gucci Air Force', brand: 'Nike', price: 4500, originalPrice: 5200, isLimited: true },
              { id: 2, name: 'Dior B23 High-Top', brand: 'Dior', price: 22000, isExclusive: true },
              { id: 3, name: 'Louis Vuitton Trainer', brand: 'Louis Vuitton', price: 28500, isVip: true },
              { id: 4, name: 'Gucci Ace Sneaker', brand: 'Gucci', price: 12800, isNew: true },
              { id: 5, name: 'Yeezy Boost 350 V2', brand: 'Adidas', price: 4800, isLimited: true },
              { id: 6, name: 'Golden Goose Superstar', brand: 'Golden Goose', price: 8900 },
              { id: 7, name: 'Balenciaga Triple S', brand: 'Balenciaga', price: 15600, isExclusive: true },
              { id: 8, name: 'Off-White Dunk Low', brand: 'Nike', price: 6200, originalPrice: 7000, isLimited: true }
            ].map((product, index) => (
              <InteractiveProductCard key={product.id} product={product} index={index} />
            ))}
          </div>

          {/* Ver toda la colección button */}
          <div className="text-center">
            <button className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-3 rounded-lg transition-colors">
              Ver toda la colección
            </button>
          </div>
        </div>
      </section>

      {/* Hottest Drops Section */}
      <section className="relative py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-black via-gray-900 to-black overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-20 w-32 h-32 bg-lime-500/20 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-40 h-40 bg-lime-500/10 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
          <div className="absolute top-1/2 left-1/2 w-24 h-24 bg-lime-500/15 rounded-full blur-lg animate-pulse" style={{ animationDelay: '2s' }}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Hottest Drops
            </h2>
            <p className="text-lg text-gray-300 max-w-2xl mx-auto">
              Los lanzamientos más esperados y exclusivos. Ediciones limitadas que no puedes perderte.
            </p>
          </div>

          {/* Hottest Drops Grid */}
          <div className="flex justify-center">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 max-w-6xl">
              {[
                { name: 'Travis Scott x Nike', brand: 'Nike', price: 8500, drop: 'Cactus Jack' },
                { name: 'Virgil Abloh x LV', brand: 'Louis Vuitton', price: 35000, drop: 'Final Collection' },
                { name: 'Kanye x Adidas', brand: 'Adidas', price: 5200, drop: 'Yeezy 700' },
                { name: 'Pharrell x Chanel', brand: 'Chanel', price: 28000, drop: 'Collaboration' },
                { name: 'Jacquemus x Nike', brand: 'Nike', price: 4800, drop: 'Le Swoosh' }
              ].map((drop, index) => (
                <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                  <div className="relative h-48 bg-gradient-to-br from-gray-900 to-gray-700 overflow-hidden">
                    {/* Limited badge */}
                    <div className="absolute top-3 left-3 z-10">
                      <span className="px-3 py-1 bg-red-500 text-white text-xs font-bold rounded-full animate-pulse">
                        LIMITADO
                      </span>
                    </div>

                    {/* Countdown or stock indicator */}
                    <div className="absolute bottom-3 left-3 right-3">
                      <div className="bg-black/50 backdrop-blur-sm rounded-lg p-2">
                        <p className="text-white text-xs font-medium">{drop.drop}</p>
                        <p className="text-lime-400 text-xs">Stock limitado</p>
                      </div>
                    </div>

                    {/* Glow effect */}
                    <div className="absolute inset-0 bg-gradient-to-t from-lime-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>

                  <div className="p-4">
                    <div className="mb-3">
                      <p className="text-xs text-gray-500 uppercase tracking-wide font-medium">{drop.brand}</p>
                      <h3 className="font-bold text-gray-900 text-sm leading-tight">{drop.name}</h3>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-lg font-bold text-lime-600">${drop.price.toLocaleString()} MXN</span>
                      <button className="w-8 h-8 bg-lime-500 hover:bg-lime-600 text-black rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Editorial Picks Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-pure-white dark:bg-mist-gray">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-godber font-bold text-pure-black dark:text-pure-white mb-4">
              Editorial Picks
            </h2>
            <p className="text-lg text-text-gray max-w-2xl mx-auto font-poppins">
              Selecciones curadas por nuestro equipo editorial. Tendencias, estilos y must-haves de la temporada.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: 'Sneakers Futuristas',
                subtitle: 'La revolución del calzado tech',
                description: 'Descubre cómo la tecnología está redefiniendo el diseño de sneakers con materiales innovadores y estética futurista.',
                tag: 'TENDENCIA'
              },
              {
                title: 'Lujo Sostenible',
                subtitle: 'Moda consciente sin comprometer el estilo',
                description: 'Las marcas de lujo abrazan la sostenibilidad con materiales reciclados y procesos de producción responsables.',
                tag: 'EDITORIAL'
              },
              {
                title: 'Colaboraciones Icónicas',
                subtitle: 'Cuando el arte se encuentra con la moda',
                description: 'Exploramos las colaboraciones más esperadas entre diseñadores, artistas y marcas de streetwear.',
                tag: 'EXCLUSIVO'
              }
            ].map((article, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                <div className="relative h-64 bg-gradient-to-br from-gray-100 to-gray-300 overflow-hidden">
                  {/* Article tag */}
                  <div className="absolute top-4 left-4 z-10">
                    <span className="px-3 py-1 bg-lime-500 text-black text-xs font-bold rounded-full">
                      {article.tag}
                    </span>
                  </div>

                  {/* Overlay on hover */}
                  <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <button className="px-6 py-3 bg-white text-black rounded-lg font-medium hover:bg-gray-100 transition-colors">
                      Leer Artículo
                    </button>
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{article.title}</h3>
                  <h4 className="text-lg text-lime-600 font-medium mb-3">{article.subtitle}</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">{article.description}</p>

                  <div className="mt-4 flex items-center justify-between">
                    <span className="text-xs text-gray-400">Por el equipo TWL</span>
                    <button className="text-lime-600 hover:text-lime-700 text-sm font-medium flex items-center gap-1">
                      Leer más
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-pure-black dark:bg-deep-pine">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-godber font-bold text-pure-white mb-4">
            Mantente al Día
          </h2>
          <p className="text-lg text-light-cloud-gray mb-8 max-w-2xl mx-auto font-poppins">
            Sé el primero en conocer sobre drops exclusivos, colaboraciones limitadas y acceso temprano a nuestras colecciones.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="<EMAIL>"
              className="flex-1 px-4 py-3 rounded-lg border border-border-gray bg-pure-white dark:bg-mist-gray text-pure-black dark:text-pure-white focus:outline-none focus:ring-2 focus:ring-lime-green font-poppins"
            />
            <button className="bg-lime-green hover:bg-lime-green/90 text-pure-black font-medium px-6 py-3 rounded-lg transition-colors whitespace-nowrap font-poppins">
              Suscribirse
            </button>
          </div>
        </div>
      </section>
    </div>
  )
}


