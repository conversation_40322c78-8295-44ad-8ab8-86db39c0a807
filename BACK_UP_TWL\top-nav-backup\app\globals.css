/* TWL Enhanced Navigation Styles - Backup Copy */
/* Contains all CSS related to the dual navigation system */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Navigation Menu Styles */
.nav-menu-transition {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-menu {
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile navigation enhancements */
.mobile-nav-item {
  @apply flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-200;
  min-width: 60px;
  min-height: 60px;
}

/* Glassmorphic effects for navigation */
.nav-glassmorphic {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Enhanced hover effects for navigation links */
.nav-link-hover {
  position: relative;
  transition: all 0.3s ease;
}

.nav-link-hover::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: #BFFF00;
  transition: width 0.3s ease;
}

.nav-link-hover:hover::after {
  width: 100%;
}

/* Flag icon hover effects */
.flag-icon {
  transition: transform 0.2s ease;
}

.flag-icon:hover {
  transform: scale(1.1);
}

/* Dropdown menu styling */
.dropdown-container {
  position: relative;
}

.dropdown-content {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 50;
  min-width: 200px;
}

/* Category navigation specific styles */
.category-nav {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.category-nav.scrolled {
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}

/* Logo hover effects */
.logo-hover {
  transition: transform 0.2s ease;
}

.logo-hover:hover {
  transform: scale(1.05);
}

/* Search bar enhancements */
.search-bar {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}

.search-bar:focus {
  background: rgba(255, 255, 255, 0.9);
  border-color: #BFFF00;
  box-shadow: 0 0 0 2px rgba(191, 255, 0, 0.2);
}

/* Mobile menu styling */
.mobile-menu {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* Action buttons (cart, wishlist, account) */
.action-button {
  transition: all 0.3s ease;
  position: relative;
}

.action-button:hover {
  background: rgba(191, 255, 0, 0.1);
  color: #84cc16;
}

/* Badge styling for cart/wishlist counts */
.count-badge {
  background: #BFFF00;
  color: black;
  font-weight: bold;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  position: absolute;
  top: -4px;
  right: -4px;
}

.count-badge.animate {
  animation: pulse 2s infinite;
}

/* Responsive navigation adjustments */
@media (max-width: 1024px) {
  .nav-desktop {
    display: none;
  }
  
  .nav-mobile {
    display: flex;
  }
}

@media (min-width: 1025px) {
  .nav-desktop {
    display: flex;
  }
  
  .nav-mobile {
    display: none;
  }
}

/* Smooth scroll behavior for navigation */
html {
  scroll-behavior: smooth;
}

/* Focus states for accessibility */
.nav-focusable:focus {
  outline: 2px solid #BFFF00;
  outline-offset: 2px;
}

/* Animation for navigation state changes */
.nav-slide-down {
  transform: translateY(0);
  transition: transform 0.3s ease;
}

.nav-slide-up {
  transform: translateY(-100%);
  transition: transform 0.3s ease;
}

/* Special styling for Limited Editions category */
.category-special {
  color: #dc2626;
}

.category-special:hover {
  color: #b91c1c;
}

.category-special::after {
  background: #dc2626;
}

/* Currency ticker styling */
.currency-ticker {
  font-family: 'Fira Code', monospace;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* Language flag container */
.flag-container {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
}

/* Ensure proper spacing in navigation */
.nav-spacing {
  padding: 0 1rem;
}

@media (min-width: 768px) {
  .nav-spacing {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .nav-spacing {
    padding: 0 2rem;
  }
}
