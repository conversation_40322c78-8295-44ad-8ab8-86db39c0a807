'use client'

import { memo, useState, useCallback } from 'react'
import { useSimpleCart, useSimpleWishlist } from '../providers/TWLProviders'
import LazyImage from './LazyImage'

const OptimizedProductCard = memo(function OptimizedProductCard({ product, priority = false }) {
  const [isHovered, setIsHovered] = useState(false)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const { addItem } = useSimpleCart()
  const { toggleWishlist, isInWishlist } = useSimpleWishlist()

  // Generate multiple product images for hover effect
  const productImages = [
    product.image || '/placeholder-shoe.jpg',
    product.hoverImage || product.image || '/placeholder-shoe-2.jpg'
  ]

  // Memoized handlers to prevent unnecessary re-renders
  const handleAddToCart = useCallback(() => {
    addItem(product, 'M', 1)
  }, [addItem, product])

  const handleToggleWishlist = useCallback(() => {
    toggleWishlist(product)
  }, [toggleWishlist, product])

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true)
    setCurrentImageIndex(1) // Switch to second image on hover
  }, [])

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false)
    setCurrentImageIndex(0) // Switch back to first image
  }, [])

  const isWishlisted = isInWishlist(product.id)

  return (
    <div
      className="bg-white rounded-2xl shadow-sm hover:shadow-xl transition-all duration-300 group border border-gray-200 hover:-translate-y-3 hover:scale-105 transform-gpu cursor-pointer"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Product Image Container */}
      <div className="relative aspect-square bg-gray-50 rounded-t-2xl overflow-hidden">
        <LazyImage
          src={productImages[currentImageIndex]}
          alt={product.name}
          width={400}
          height={400}
          priority={priority}
          className="w-full h-full object-cover transition-all duration-500 ease-in-out"
        />

        {/* Wishlist Heart - Top Right */}
        <button
          onClick={handleToggleWishlist}
          className="absolute top-3 right-3 w-10 h-10 flex items-center justify-center transition-all duration-200 hover:scale-110"
          aria-label={isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
        >
          <svg
            className={`w-6 h-6 transition-colors duration-200 ${
              isWishlisted
                ? 'fill-lime-green text-lime-green'
                : 'fill-none text-red-800 hover:text-red-600'
            }`}
            stroke="currentColor"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button>

        {/* Add to Cart Button - Bottom Right */}
        <div className="absolute bottom-3 right-3 group/cart">
          {/* Pulsating ring effect - only on hover */}
          <div className="absolute inset-0 w-10 h-10 bg-lime-green rounded-full animate-ping opacity-0 group-hover/cart:opacity-75 transition-opacity duration-200"></div>

          {/* Main button */}
          <button
            onClick={handleAddToCart}
            className="relative w-10 h-10 bg-lime-green hover:bg-lime-green text-black rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg font-medium"
            aria-label={`Add ${product.name} to cart`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
          </button>
        </div>
      </div>

      {/* Product Info */}
      <div className="p-4">
        {/* Brand */}
        <p className="text-xs text-gray-500 uppercase tracking-wider font-medium mb-1 font-inter">
          {product.brand}
        </p>

        {/* Product Name */}
        <h3 className="font-medium text-black text-sm leading-tight mb-2 line-clamp-2 font-poppins">
          {product.name}
        </h3>

        {/* Price */}
        <div className="flex items-baseline space-x-2">
          <span className="text-lg font-bold text-lime-green-dark font-poppins">
            ${product.price?.toLocaleString() || '0'}
          </span>
          {(product.originalPrice || product.price) && (
            <span className="text-sm text-gray-400 line-through font-inter">
              ${(product.originalPrice || Math.round(product.price * 1.2))?.toLocaleString()}
            </span>
          )}
        </div>
      </div>
    </div>
  )
})

export default OptimizedProductCard
