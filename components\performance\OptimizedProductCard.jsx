'use client'

import { memo, useState, useCallback } from 'react'
import { useSimpleCart, useSimpleWishlist } from '../providers/TWLProviders'
import LazyImage from './LazyImage'

const OptimizedProductCard = memo(function OptimizedProductCard({ product, priority = false }) {
  const [isHovered, setIsHovered] = useState(false)
  const { addItem } = useSimpleCart()
  const { toggleWishlist, isInWishlist } = useSimpleWishlist()

  // Memoized handlers to prevent unnecessary re-renders
  const handleAddToCart = useCallback(() => {
    addItem(product, 'M', 1)
  }, [addItem, product])

  const handleToggleWishlist = useCallback(() => {
    toggleWishlist(product)
  }, [toggleWishlist, product])

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true)
  }, [])

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false)
  }, [])

  const isWishlisted = isInWishlist(product.id)

  return (
    <div
      className="bg-white rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 group border border-gray-200"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Product Image Container */}
      <div className="relative aspect-square bg-gray-50 rounded-t-2xl overflow-hidden">
        <LazyImage
          src={product.image || '/placeholder-shoe.jpg'}
          alt={product.name}
          width={400}
          height={400}
          priority={priority}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />

        {/* Wishlist Heart - Top Right */}
        <button
          onClick={handleToggleWishlist}
          className="absolute top-3 right-3 w-8 h-8 flex items-center justify-center transition-all duration-200 hover:scale-110"
          aria-label={isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
        >
          <svg
            className={`w-5 h-5 transition-colors duration-200 ${
              isWishlisted
                ? 'fill-lime-green text-lime-green'
                : 'fill-none text-gray-400 hover:text-lime-green'
            }`}
            stroke="currentColor"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button>

        {/* Add to Cart Button - Bottom Right */}
        <button
          onClick={handleAddToCart}
          className="absolute bottom-3 right-3 w-10 h-10 bg-lime-green hover:bg-lime-600 text-black rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg font-medium"
          aria-label={`Add ${product.name} to cart`}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
        </button>
      </div>

      {/* Product Info */}
      <div className="p-4">
        {/* Brand */}
        <p className="text-xs text-gray-500 uppercase tracking-wider font-medium mb-1 font-inter">
          {product.brand}
        </p>

        {/* Product Name */}
        <h3 className="font-medium text-black text-sm leading-tight mb-2 line-clamp-2 font-poppins">
          {product.name}
        </h3>

        {/* Price */}
        <div className="flex items-baseline space-x-2">
          <span className="text-lg font-bold text-lime-green font-poppins">
            ${product.price?.toLocaleString() || '0'}
          </span>
          {product.originalPrice && (
            <span className="text-sm text-gray-400 line-through font-inter">
              ${product.originalPrice.toLocaleString()}
            </span>
          )}
        </div>
      </div>
    </div>
  )
})

export default OptimizedProductCard
