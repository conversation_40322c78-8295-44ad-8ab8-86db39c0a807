'use client'

import { memo, useState, useCallback } from 'react'
import { useSimpleCart, useSimpleWishlist } from '../providers/TWLProviders'
import LazyImage from './LazyImage'

const OptimizedProductCard = memo(function OptimizedProductCard({ product, priority = false }) {
  const [isHovered, setIsHovered] = useState(false)
  const { addItem } = useSimpleCart()
  const { toggleWishlist, isInWishlist } = useSimpleWishlist()

  // Memoized handlers to prevent unnecessary re-renders
  const handleAddToCart = useCallback(() => {
    addItem(product, 'M', 1)
  }, [addItem, product])

  const handleToggleWishlist = useCallback(() => {
    toggleWishlist(product)
  }, [toggleWishlist, product])

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true)
  }, [])

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false)
  }, [])

  const isWishlisted = isInWishlist(product.id)

  return (
    <div 
      className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="relative h-64 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
        {/* Product Image */}
        <LazyImage
          src={product.image || '/placeholder-shoe.jpg'}
          alt={product.name}
          width={400}
          height={300}
          priority={priority}
          className="w-full h-full object-cover"
        />

        {/* Product badges */}
        <div className="absolute top-3 left-3 z-10 space-y-1">
          {product.isLimited && (
            <span className="block px-2 py-1 bg-red-500 text-white text-xs font-medium rounded-full">
              LIMITADO
            </span>
          )}
          {product.isExclusive && (
            <span className="block px-2 py-1 bg-purple-500 text-white text-xs font-medium rounded-full">
              EXCLUSIVO
            </span>
          )}
          {product.isNew && (
            <span className="block px-2 py-1 bg-green-500 text-white text-xs font-medium rounded-full">
              NUEVO
            </span>
          )}
        </div>
        
        {/* Wishlist button */}
        <button 
          onClick={handleToggleWishlist}
          className={`absolute top-3 right-3 p-2 backdrop-blur-sm rounded-full hover:bg-white transition-all duration-300 ${
            isWishlisted ? 'bg-red-100 text-red-500' : 'bg-white/80 text-gray-600'
          }`}
          aria-label={isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
        >
          <svg className="w-5 h-5" fill={isWishlisted ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button>

        {/* Quick view overlay - only render when hovered for performance */}
        {isHovered && (
          <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <button className="px-6 py-3 bg-white text-black rounded-lg font-medium hover:bg-gray-100 transition-colors">
              Vista Rápida
            </button>
          </div>
        )}
      </div>
      
      <div className="p-6">
        <div className="mb-3">
          <p className="text-xs text-gray-500 uppercase tracking-wide font-medium">{product.brand}</p>
          <h3 className="font-semibold text-gray-900 text-lg leading-tight line-clamp-2">{product.name}</h3>
          {product.description && (
            <p className="text-sm text-gray-600 mt-1 line-clamp-2">{product.description}</p>
          )}
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex flex-col">
            <span className="text-xl font-bold text-lime-600">
              ${product.price?.toLocaleString() || '0'} MXN
            </span>
            {product.originalPrice && (
              <span className="text-sm text-gray-400 line-through">
                ${product.originalPrice.toLocaleString()} MXN
              </span>
            )}
          </div>
          <button 
            onClick={handleAddToCart}
            className="px-6 py-2 bg-lime-500 hover:bg-lime-600 text-black rounded-lg font-medium transition-all duration-300 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-lime-500 focus:ring-offset-2"
            aria-label={`Add ${product.name} to cart`}
          >
            Agregar al Carrito
          </button>
        </div>
      </div>
    </div>
  )
})

export default OptimizedProductCard
