'use client'

import { FlagIcons } from '../ui/FlagIcons'

export default function MobileMenu({ 
  isOpen, 
  onClose, 
  searchQuery, 
  setSearchQuery, 
  handleSearch,
  selectedLanguage,
  selectedCurrency,
  handleLanguageChange,
  handleCurrencyChange,
  languages,
  currencies,
  navCategories 
}) {
  if (!isOpen) return null

  return (
    <div className="xl:hidden fixed inset-0 z-50 bg-black/20" onClick={onClose}>
      <div 
        className="w-[85%] h-full bg-white/95 backdrop-blur-xl border-r border-white/20 shadow-lg overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="px-4 py-6 space-y-4 mt-16">
          <form onSubmit={handleSearch} className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Buscar productos..."
              className="w-full px-4 py-3 pl-10 pr-4 text-gray-700 bg-white/70 border border-white/40 rounded-full focus:outline-none focus:ring-2 focus:ring-lime-500"
            />
            <button type="submit" className="absolute inset-y-0 left-0 flex items-center pl-3">
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
          </form>

          {/* Main Navigation */}
          <div className="space-y-2 mb-6">
            <h3 className="text-sm font-semibold text-gray-900 px-4 mb-3">Navegación Principal</h3>
            {[
              { name: 'Tienda', href: '/shop' },
              { name: 'IA Features', href: '/ai-features' },
              { name: 'Social', href: '/community' }
            ].map((item) => (
              <a
                key={item.name}
                href={item.href}
                className="block px-4 py-3 text-gray-700 hover:text-lime-600 hover:bg-lime-50 rounded-lg transition-colors font-medium"
                onClick={onClose}
              >
                {item.name}
              </a>
            ))}
          </div>

          {/* Category Navigation */}
          <div className="space-y-2 mb-6">
            <h3 className="text-sm font-semibold text-gray-900 px-4 mb-3">Categorías</h3>
            {navCategories.map((category) => (
              <a
                key={category.name}
                href={category.href}
                className={`block px-4 py-3 rounded-lg transition-colors font-medium ${
                  category.isSpecial 
                    ? 'text-red-600 hover:text-red-700 hover:bg-red-50' 
                    : 'text-gray-700 hover:text-lime-600 hover:bg-lime-50'
                }`}
                onClick={onClose}
              >
                {category.name}
              </a>
            ))}
          </div>

          {/* Language and Currency Selection */}
          <div className="border-t border-gray-200 pt-4">
            <h3 className="text-sm font-semibold text-gray-900 px-4 mb-3">Preferencias</h3>
            
            {/* Mobile Language Selection */}
            <div className="px-4 py-2">
              <label className="text-xs text-gray-500 mb-2 block">Idioma</label>
              <div className="space-y-1">
                {languages.map((language) => (
                  <button
                    key={language.code}
                    onClick={() => {
                      handleLanguageChange(language.code)
                      onClose()
                    }}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors ${
                      selectedLanguage === language.code ? 'bg-lime-50 text-lime-600' : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {(() => {
                      const FlagComponent = FlagIcons[language.code]
                      return FlagComponent ? <FlagComponent size={18} /> : null
                    })()}
                    <span className="font-medium">{language.name}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Mobile Currency Selection */}
            <div className="px-4 py-2">
              <label className="text-xs text-gray-500 mb-2 block">Moneda</label>
              <div className="space-y-1">
                {currencies.map((currency) => (
                  <button
                    key={currency.code}
                    onClick={() => {
                      handleCurrencyChange(currency.code)
                      onClose()
                    }}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors ${
                      selectedCurrency === currency.code ? 'bg-lime-50 text-lime-600' : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <span className="text-lg">{currency.flag}</span>
                    <span className="font-medium">{currency.code} - {currency.symbol}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
