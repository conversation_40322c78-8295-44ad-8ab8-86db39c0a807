import { Inter, Playfair_Display, Fira_Code, Poppins } from 'next/font/google'
import './globals.css'
import InteractiveHeader from '../components/layout/InteractiveHeader'
import TWLProviders from '../components/providers/TWLProviders'
import PerformanceMonitor from '../components/debug/PerformanceMonitor'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap'
})

const playfair = Playfair_Display({ 
  subsets: ['latin'],
  variable: '--font-playfair',
  display: 'swap'
})

const firaCode = Fira_Code({ 
  subsets: ['latin'],
  variable: '--font-fira-code',
  display: 'swap'
})

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-poppins',
  display: 'swap'
})

export const metadata = {
  title: {
    default: 'The White Laces - Luxury Streetwear & Premium Sneakers',
    template: '%s | The White Laces'
  },
  description: 'Descubre la colección más exclusiva de sneakers premium, ediciones limitadas y streetwear de lujo. Nike, Jordan, Dior, Gucci y más marcas icónicas.',
  keywords: [
    'sneakers premium',
    'streetwear lujo',
    'ediciones limitadas',
    'Nike',
    'Jordan',
    'Dior',
    'Gucci',
    'zapatos exclusivos',
    'moda urbana',
    'coleccionables'
  ],
  authors: [{ name: 'The White Laces Team' }],
  creator: 'The White Laces',
  publisher: 'The White Laces',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://thewhitelaces.com'),
  alternates: {
    canonical: '/',
    languages: {
      'es-MX': '/es-MX',
      'en-US': '/en-US',
      'pt-BR': '/pt-BR',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'es_MX',
    url: 'https://thewhitelaces.com',
    title: 'The White Laces - Luxury Streetwear & Premium Sneakers',
    description: 'Descubre la colección más exclusiva de sneakers premium y streetwear de lujo.',
    siteName: 'The White Laces',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'The White Laces - Luxury Streetwear',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The White Laces - Luxury Streetwear & Premium Sneakers',
    description: 'Descubre la colección más exclusiva de sneakers premium y streetwear de lujo.',
    images: ['/twitter-image.jpg'],
    creator: '@thewhitelaces',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
  },
}

export default function RootLayout({ children }) {
  return (
    <html 
      lang="es-MX" 
      className={`${inter.variable} ${playfair.variable} ${firaCode.variable} ${poppins.variable}`}
    >
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#BFFF00" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body 
        className={`${inter.variable} ${playfair.variable} ${firaCode.variable} ${poppins.variable} font-poppins antialiased`}
        suppressHydrationWarning
      >
        <TWLProviders>
          <div className="min-h-screen bg-white text-black transition-colors duration-300">
            {/* Enhanced Interactive Header */}
            <InteractiveHeader />

            <main className="pt-32 lg:pt-36">
              {children}
            </main>

          {/* TWL Footer */}
          <footer className="bg-black text-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {/* Brand */}
                <div className="lg:col-span-1">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-lime-400 to-lime-600 rounded-full flex items-center justify-center">
                      <span className="text-black font-bold text-xl">W</span>
                    </div>
                    <div>
                      <span className="text-xl font-bold">The White Laces</span>
                      <div className="text-sm text-gray-400">Luxury Streetwear</div>
                    </div>
                  </div>
                  <p className="text-gray-400 text-sm leading-relaxed">
                    Redefiniendo el lujo urbano con sneakers premium, ediciones limitadas y drops exclusivos de las mejores marcas del mundo.
                  </p>
                </div>

                {/* Shop */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Tienda</h3>
                  <ul className="space-y-2 text-sm text-gray-400">
                    <li><a href="/shop/sneakers" className="hover:text-lime-400 transition-colors">Sneakers</a></li>
                    <li><a href="/shop/limited-editions" className="hover:text-lime-400 transition-colors">Ediciones Limitadas</a></li>
                    <li><a href="/shop/brands" className="hover:text-lime-400 transition-colors">Marcas</a></li>
                    <li><a href="/shop/new-arrivals" className="hover:text-lime-400 transition-colors">Nuevos Lanzamientos</a></li>
                  </ul>
                </div>

                {/* Support */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Soporte</h3>
                  <ul className="space-y-2 text-sm text-gray-400">
                    <li><a href="/help" className="hover:text-lime-400 transition-colors">Centro de Ayuda</a></li>
                    <li><a href="/shipping" className="hover:text-lime-400 transition-colors">Envíos</a></li>
                    <li><a href="/returns" className="hover:text-lime-400 transition-colors">Devoluciones</a></li>
                    <li><a href="/contact" className="hover:text-lime-400 transition-colors">Contacto</a></li>
                  </ul>
                </div>

                {/* Connect */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Conecta</h3>
                  <div className="flex space-x-4 mb-4">
                    <a href="#" className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center hover:bg-lime-500 transition-colors">
                      <span className="text-xs font-bold">IG</span>
                    </a>
                    <a href="#" className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center hover:bg-lime-500 transition-colors">
                      <span className="text-xs font-bold">TT</span>
                    </a>
                    <a href="#" className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center hover:bg-lime-500 transition-colors">
                      <span className="text-xs font-bold">X</span>
                    </a>
                  </div>
                  <p className="text-gray-400 text-sm">
                    Síguenos para drops exclusivos y acceso temprano.
                  </p>
                </div>
              </div>

              {/* Bottom */}
              <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p className="text-gray-400 text-sm">
                  © 2025 The White Laces. Todos los derechos reservados.
                </p>
                <div className="flex space-x-6 mt-4 md:mt-0">
                  <a href="/privacy" className="text-gray-400 hover:text-lime-400 text-sm transition-colors">Privacidad</a>
                  <a href="/terms" className="text-gray-400 hover:text-lime-400 text-sm transition-colors">Términos</a>
                  <a href="/cookies" className="text-gray-400 hover:text-lime-400 text-sm transition-colors">Cookies</a>
                </div>
              </div>
            </div>
          </footer>
          </div>

          {/* Performance Monitor (Development Only) */}
          <PerformanceMonitor />
        </TWLProviders>
      </body>
    </html>
  )
}
