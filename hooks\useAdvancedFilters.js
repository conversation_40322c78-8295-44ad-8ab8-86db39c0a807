import { useState, useCallback, useMemo } from 'react'

export function useAdvancedFilters(products) {
  // Basic filters
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedBrand, setSelectedBrand] = useState('all')
  const [sortBy, setSortBy] = useState('newest')

  // Advanced filters
  const [priceRange, setPriceRange] = useState([0, 50000])
  const [selectedSizes, setSelectedSizes] = useState([])
  const [selectedColors, setSelectedColors] = useState([])
  
  // Special filters
  const [isOnSale, setIsOnSale] = useState(false)
  const [isLimited, setIsLimited] = useState(false)
  const [isNew, setIsNew] = useState(false)

  // Clear all filters
  const clearAllFilters = useCallback(() => {
    setSearchQuery('')
    setSelectedCategory('all')
    setSelectedBrand('all')
    setSortBy('newest')
    setPriceRange([0, 50000])
    setSelectedSizes([])
    setSelectedColors([])
    setIsOnSale(false)
    setIsLimited(false)
    setIsNew(false)
  }, [])

  // Apply all filters
  const filteredProducts = useMemo(() => {
    let filtered = [...products]

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.brand.toLowerCase().includes(query) ||
        (product.description && product.description.toLowerCase().includes(query))
      )
    }

    // Category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === selectedCategory)
    }

    // Brand filter
    if (selectedBrand !== 'all') {
      filtered = filtered.filter(product => product.brand === selectedBrand)
    }

    // Price range filter
    filtered = filtered.filter(product => 
      product.price >= priceRange[0] && product.price <= priceRange[1]
    )

    // Size filter
    if (selectedSizes.length > 0) {
      filtered = filtered.filter(product => 
        product.sizes && product.sizes.some(size => selectedSizes.includes(size))
      )
    }

    // Color filter
    if (selectedColors.length > 0) {
      filtered = filtered.filter(product => 
        product.colors && product.colors.some(color => selectedColors.includes(color))
      )
    }

    // Special filters
    if (isOnSale) {
      filtered = filtered.filter(product => product.originalPrice && product.originalPrice > product.price)
    }

    if (isLimited) {
      filtered = filtered.filter(product => product.isLimited)
    }

    if (isNew) {
      filtered = filtered.filter(product => product.isNew)
    }

    // Sorting
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price)
        break
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price)
        break
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name))
        break
      case 'brand':
        filtered.sort((a, b) => a.brand.localeCompare(b.brand))
        break
      case 'popularity':
        filtered.sort((a, b) => (b.popularity || 0) - (a.popularity || 0))
        break
      default: // newest
        filtered.sort((a, b) => b.id - a.id)
    }

    return filtered
  }, [
    products,
    searchQuery,
    selectedCategory,
    selectedBrand,
    sortBy,
    priceRange,
    selectedSizes,
    selectedColors,
    isOnSale,
    isLimited,
    isNew
  ])

  // Get filter statistics
  const filterStats = useMemo(() => {
    const stats = {
      totalProducts: products.length,
      filteredProducts: filteredProducts.length,
      onSaleCount: products.filter(p => p.originalPrice && p.originalPrice > p.price).length,
      limitedCount: products.filter(p => p.isLimited).length,
      newCount: products.filter(p => p.isNew).length,
      priceRange: {
        min: Math.min(...products.map(p => p.price)),
        max: Math.max(...products.map(p => p.price))
      }
    }
    return stats
  }, [products, filteredProducts])

  return {
    // Filter states
    searchQuery,
    setSearchQuery,
    selectedCategory,
    setSelectedCategory,
    selectedBrand,
    setSelectedBrand,
    sortBy,
    setSortBy,
    priceRange,
    setPriceRange,
    selectedSizes,
    setSelectedSizes,
    selectedColors,
    setSelectedColors,
    isOnSale,
    setIsOnSale,
    isLimited,
    setIsLimited,
    isNew,
    setIsNew,
    
    // Results
    filteredProducts,
    filterStats,
    clearAllFilters
  }
}
