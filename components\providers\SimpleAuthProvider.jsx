'use client'

import { createContext, useContext, useState, useEffect } from 'react'

const AuthContext = createContext()

export function SimpleAuthProvider({ children }) {
  const [user, setUser] = useState(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isHydrated, setIsHydrated] = useState(false)

  // Hydration effect
  useEffect(() => {
    setIsHydrated(true)
  }, [])

  // Load user from localStorage on mount
  useEffect(() => {
    if (!isHydrated) return

    try {
      const savedUser = localStorage.getItem('twl-simple-user')
      if (savedUser) {
        const parsedUser = JSON.parse(savedUser)
        setUser(parsedUser)
        setIsAuthenticated(true)
      }
    } catch (error) {
      console.error('Error loading user from localStorage:', error)
    } finally {
      setIsLoading(false)
    }
  }, [isHydrated])

  // Save user to localStorage whenever it changes
  useEffect(() => {
    if (!isHydrated) return

    try {
      if (user) {
        localStorage.setItem('twl-simple-user', JSON.stringify(user))
      } else {
        localStorage.removeItem('twl-simple-user')
      }
    } catch (error) {
      console.error('Error saving user to localStorage:', error)
    }
  }, [isHydrated, user])

  // Authentication Actions
  const login = async (email, password) => {
    setIsLoading(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock authentication - in real app, validate credentials
      if (email && password) {
        const mockUser = {
          id: Date.now(),
          email,
          firstName: email.split('@')[0],
          lastName: 'Usuario',
          avatar: null,
          preferences: {
            language: 'es-MX',
            currency: 'MXN',
            notifications: true
          },
          createdAt: new Date().toISOString(),
          lastLoginAt: new Date().toISOString()
        }
        
        setUser(mockUser)
        setIsAuthenticated(true)
        showToast(`¡Bienvenido, ${mockUser.firstName}!`)
        
        return { success: true, user: mockUser }
      } else {
        throw new Error('Credenciales inválidas')
      }
    } catch (error) {
      showToast(`Error: ${error.message}`)
      return { success: false, error: error.message }
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (userData) => {
    setIsLoading(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      const newUser = {
        id: Date.now(),
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        avatar: null,
        preferences: {
          language: 'es-MX',
          currency: 'MXN',
          notifications: true
        },
        createdAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString()
      }
      
      setUser(newUser)
      setIsAuthenticated(true)
      showToast(`¡Cuenta creada! Bienvenido, ${newUser.firstName}!`)
      
      return { success: true, user: newUser }
    } catch (error) {
      showToast(`Error: ${error.message}`)
      return { success: false, error: error.message }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    setUser(null)
    setIsAuthenticated(false)
    showToast('Sesión cerrada correctamente')
  }

  const updateProfile = async (updates) => {
    if (!isAuthenticated) return { success: false, error: 'No autenticado' }
    
    setIsLoading(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800))
      
      const updatedUser = {
        ...user,
        ...updates,
        updatedAt: new Date().toISOString()
      }
      
      setUser(updatedUser)
      showToast('Perfil actualizado correctamente')
      
      return { success: true, user: updatedUser }
    } catch (error) {
      showToast(`Error: ${error.message}`)
      return { success: false, error: error.message }
    } finally {
      setIsLoading(false)
    }
  }

  const resetPassword = async (email) => {
    setIsLoading(true)
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      showToast('Enlace de recuperación enviado a tu email')
      return { success: true }
    } catch (error) {
      showToast(`Error: ${error.message}`)
      return { success: false, error: error.message }
    } finally {
      setIsLoading(false)
    }
  }

  // Simple toast notification
  const showToast = (message) => {
    const toast = document.createElement('div')
    toast.className = 'fixed top-20 right-4 bg-lime-500 text-black px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300'
    toast.textContent = message
    document.body.appendChild(toast)
    
    // Animate in
    setTimeout(() => {
      toast.style.transform = 'translateX(0)'
      toast.style.opacity = '1'
    }, 100)
    
    // Remove after 3 seconds
    setTimeout(() => {
      toast.style.transform = 'translateX(100%)'
      toast.style.opacity = '0'
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast)
        }
      }, 300)
    }, 3000)
  }

  const value = {
    // State
    user,
    isAuthenticated,
    isLoading,
    
    // Actions
    login,
    register,
    logout,
    updateProfile,
    resetPassword,
    
    // Utilities
    showToast
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useSimpleAuth() {
  const context = useContext(AuthContext)
  
  if (!context) {
    throw new Error('useSimpleAuth must be used within a SimpleAuthProvider')
  }
  
  return context
}
