'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useCart } from '@/contexts/CartContext'
import { useWishlist } from '@/contexts/WishlistContext'
import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent } from '@/components/ui/Card'
import Badge from '@/components/ui/Badge'
import { TransitionLink } from '@/components/transitions/RouteTransitionProvider'

export default function MobileProductCard({ product, index = 0 }) {
  const { addItem } = useCart()
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist()
  const { isAuthenticated } = useAuth()
  const [selectedSize, setSelectedSize] = useState('')
  const [showSizeSelector, setShowSizeSelector] = useState(false)
  const [isAddingToCart, setIsAddingToCart] = useState(false)

  const isWishlisted = isInWishlist(product.id)
  const hasDiscount = product.originalPrice && product.originalPrice > product.price
  const discountPercentage = hasDiscount 
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0

  const handleAddToCart = async () => {
    if (!selectedSize) {
      setShowSizeSelector(true)
      return
    }

    setIsAddingToCart(true)
    await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API call
    
    addItem(product.id, selectedSize, 1)
    setShowSizeSelector(false)
    setSelectedSize('')
    setIsAddingToCart(false)
  }

  const handleWishlistToggle = () => {
    if (isWishlisted) {
      removeFromWishlist(product.id)
    } else {
      addToWishlist(product)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.4, 
        delay: index * 0.1,
        type: "spring",
        stiffness: 100
      }}
      className="w-full"
    >
      <Card variant="default" className="group overflow-hidden">
        <CardContent className="p-0">
          {/* Product Image */}
          <div className="relative aspect-square overflow-hidden">
            {/* Placeholder Image */}
            <div className="w-full h-full bg-neutral-100 dark:bg-neutral-800 flex items-center justify-center">
              <span className="text-4xl">👟</span>
            </div>

            {/* Badges */}
            <div className="absolute top-2 left-2 flex flex-col gap-1">
              {product.isNew && (
                <Badge variant="primary" size="sm" className="bg-secondary text-white">
                  Nuevo
                </Badge>
              )}
              {product.isLimited && (
                <Badge variant="primary" size="sm" className="bg-error text-white">
                  Limitado
                </Badge>
              )}
              {hasDiscount && (
                <Badge variant="primary" size="sm" className="bg-green-500 text-white">
                  -{discountPercentage}%
                </Badge>
              )}
            </div>

            {/* Wishlist Button */}
            <motion.button
              onClick={handleWishlistToggle}
              className="absolute top-2 right-2 p-2 rounded-full glass backdrop-blur-sm"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <motion.svg
                className={`w-6 h-6 ${isWishlisted ? 'text-lime-green' : 'text-red-800 hover:text-red-600'}`}
                fill={isWishlisted ? 'currentColor' : 'none'}
                stroke="currentColor"
                viewBox="0 0 24 24"
                animate={{ scale: isWishlisted ? [1, 1.2, 1] : 1 }}
                transition={{ duration: 0.3 }}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </motion.svg>
            </motion.button>

            {/* Quick Actions Overlay */}
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
              <TransitionLink href={`/product/${product.id}`}>
                <motion.button
                  className="px-4 py-2 bg-white/90 text-neutral-900 rounded-full font-medium"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Ver detalles
                </motion.button>
              </TransitionLink>
            </div>
          </div>

          {/* Product Info */}
          <div className="p-4 space-y-3">
            {/* Brand & Name */}
            <div>
              <p className="text-xs text-neutral font-medium uppercase tracking-wide">
                {product.brand}
              </p>
              <h3 className="text-sm font-semibold text-neutral-900 dark:text-white line-clamp-2 leading-tight">
                {product.name}
              </h3>
            </div>

            {/* Rating */}
            <div className="flex items-center gap-1">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <svg
                    key={i}
                    className={`w-3 h-3 ${i < Math.floor(product.rating) ? 'text-secondary' : 'text-neutral/30'}`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <span className="text-xs text-neutral">
                ({product.reviews})
              </span>
            </div>

            {/* Price */}
            <div className="flex items-center gap-2">
              <span className="text-lg font-bold text-neutral-900 dark:text-white">
                ${product.price?.toLocaleString()} MXN
              </span>
              {hasDiscount && (
                <span className="text-sm text-neutral line-through">
                  ${product.originalPrice?.toLocaleString()}
                </span>
              )}
            </div>

            {/* Size Selector */}
            <AnimatePresence>
              {showSizeSelector && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-2"
                >
                  <p className="text-xs text-neutral font-medium">Selecciona tu talla:</p>
                  <div className="grid grid-cols-4 gap-2">
                    {product?.variants?.[0]?.sizes?.slice(0, 8).map((size) => (
                      <motion.button
                        key={size}
                        onClick={() => setSelectedSize(size)}
                        className={`text-xs py-2 px-3 rounded-lg border transition-all ${
                          selectedSize === size
                            ? 'bg-secondary text-white border-secondary'
                            : 'border-neutral/30 text-neutral hover:border-secondary'
                        }`}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {size}
                      </motion.button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Add to Cart Button */}
            <div className="relative">
              {/* Pulsating ring effect for cart button */}
              {!isAddingToCart && (
                <div className="absolute inset-0 bg-lime-green rounded-xl animate-ping opacity-25"></div>
              )}

              <motion.button
                onClick={handleAddToCart}
                disabled={isAddingToCart}
                className={`relative w-full py-3 rounded-xl font-medium transition-all ${
                  selectedSize && !showSizeSelector
                    ? 'bg-secondary text-white hover:bg-secondary-dark'
                    : 'bg-primary text-white hover:bg-primary-dark hover:shadow-lg'
                } ${isAddingToCart ? 'opacity-50 cursor-not-allowed' : ''}`}
                whileHover={{ scale: isAddingToCart ? 1 : 1.02 }}
                whileTap={{ scale: isAddingToCart ? 1 : 0.98 }}
              >
              <AnimatePresence mode="wait">
                {isAddingToCart ? (
                  <motion.div
                    key="loading"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="flex items-center justify-center gap-2"
                  >
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Agregando...
                  </motion.div>
                ) : selectedSize && !showSizeSelector ? (
                  <motion.span
                    key="confirm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    Confirmar compra
                  </motion.span>
                ) : (
                  <motion.span
                    key="select"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    Agregar al carrito
                  </motion.span>
                )}
              </AnimatePresence>
              </motion.button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
