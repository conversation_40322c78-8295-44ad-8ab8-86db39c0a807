'use client'

import { SimpleAuthProvider } from './SimpleAuthProvider'
import { SimpleCartProvider } from './SimpleCartProvider'
import { SimpleWishlistProvider } from './SimpleWishlistProvider'

export function TWLProviders({ children }) {
  return (
    <SimpleAuthProvider>
      <SimpleCartProvider>
        <SimpleWishlistProvider>
          {children}
        </SimpleWishlistProvider>
      </SimpleCartProvider>
    </SimpleAuthProvider>
  )
}

// Export hooks for easy access
export { useSimpleAuth } from './SimpleAuthProvider'
export { useSimpleCart } from './SimpleCartProvider'
export { useSimpleWishlist } from './SimpleWishlistProvider'
