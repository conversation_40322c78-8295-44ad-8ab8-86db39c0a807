/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // TWL Hype Luxury Footwear Color System (2025)
        primary: {
          DEFAULT: '#BFFF00', // Neon Volt Lime
          dark: '#A6E600',    // Neon Volt Lime (Dark)
          light: '#CCFF33',   // Neon Volt Lime (Light)
          50: '#F7FFE6',
          100: '#EFFFCC',
          200: '#E6FF99',
          300: '#DCFF66',
          400: '#D3FF33',
          500: '#BFFF00',
          600: '#A6E600',
          700: '#8CCC00',
          800: '#73B300',
          900: '#599900'
        },
        secondary: {
          DEFAULT: '#B0B0B0', // Chrome Metallic
          dark: '#9A9A9A',    // Chrome Metallic (Dark)
          light: '#C6C6C6',   // Chrome Metallic (Light)
          50: '#F5F5F5',
          100: '#EBEBEB',
          200: '#D6D6D6',
          300: '#C1C1C1',
          400: '#ACACAC',
          500: '#B0B0B0',
          600: '#9A9A9A',
          700: '#858585',
          800: '#707070',
          900: '#5B5B5B'
        },
        // Hype Luxury Footwear Core Colors
        accent: {
          DEFAULT: '#D9D9D9', // Platinum Silver
          dark: '#C3C3C3',    // Platinum Silver (Dark)
          light: '#EFEFEF',   // Platinum Silver (Light)
          50: '#F9F9F9',
          100: '#F0F0F0',
          200: '#E6E6E6',
          300: '#DDDDDD',
          400: '#D3D3D3',
          500: '#D9D9D9',
          600: '#C3C3C3',
          700: '#ADADAD',
          800: '#979797',
          900: '#818181'
        },
        neutral: {
          DEFAULT: '#6C757D', // Cool Gray (Legacy)
          50: '#F9FAFB',      // Ice White
          100: '#D9D9D9',     // Platinum Silver
          200: '#B0B0B0',     // Chrome Metallic
          300: '#9A9A9A',
          400: '#6C757D',
          500: '#495057',
          600: '#343A40',
          700: '#2A2A2A',     // Graphite Gray
          800: '#1A1A1A',
          900: '#0B0B0B',     // Jet Black
        },
        // Background Colors
        background: {
          light: '#F9FAFB',   // Ice White
          dark: '#1A1A1A',    // Dark Grey (softer than jet black)
          overlay: '#2A2A2A', // Graphite Gray
        },
        // Text Colors
        text: {
          primary: '#0B0B0B',   // Jet Black
          secondary: '#2A2A2A', // Graphite Gray
          tertiary: '#B0B0B0',  // Chrome Metallic
          inverse: '#F9FAFB',   // Ice White
        },
        // Border Colors
        border: {
          DEFAULT: '#D9D9D9',   // Platinum Silver
          light: '#B0B0B0',     // Chrome Metallic
          dark: '#2A2A2A',      // Graphite Gray
        },
        // Legacy status colors (keeping for existing components)
        success: {
          DEFAULT: '#28C76F',
          dark: '#24B364',
          light: '#4DD888',
        },
        error: {
          DEFAULT: '#FF4C51',
          dark: '#E64449',
          light: '#FF6B6F',
        },
        warning: {
          DEFAULT: '#FFB800',
          dark: '#E6A600',
          light: '#FFC533',
        },
        info: {
          DEFAULT: '#00BAD1',
          dark: '#00A7BC',
          light: '#33C7DB',
        },

        // Hype Luxury Footwear Specific Colors
        'ice-white': '#F9FAFB',
        'platinum-silver': '#D9D9D9',
        'jet-black': '#0B0B0B',
        'dark-grey': '#1A1A1A',
        'neon-volt-lime': '#BFFF00',
        'graphite-gray': '#2A2A2A',
        'chrome-metallic': '#B0B0B0',

        // TWL Brand Colors (Used in components)
        'pure-white': '#FFFFFF',
        'pure-black': '#000000',
        'lime-green': '#BFFF00',
        'lime-green-dark': '#9FD700',
        'light-cloud-gray': '#F8F9FA',
        'mist-gray': '#1E2127',
        'deep-pine': '#14161A',
        'text-gray': '#6B7280',
        'border-gray': '#F8F9FA',

        // Glassmorphism Overlays (Updated for new palette)
        'frosted-light': 'rgba(249,250,251,0.08)',
        'frosted-dark': 'rgba(11,11,11,0.08)',
        'frosted-overlay': 'rgba(217,217,217,0.15)',
      },
      fontFamily: {
        // TWL Typography System (2025 Luxury Streetwear)
        'montserrat': ['Montserrat', 'sans-serif'], // Headings, product names, hero banners
        'inter': ['Inter', 'Helvetica Neue', 'sans-serif'], // Body text, UI text, product descriptions
        'fira': ['Fira Code', 'JetBrains Mono', 'monospace'], // Console logs, code snippets
        'poppins': ['Poppins', 'sans-serif'], // Primary TWL font
        'godber': ['Godber', 'serif'], // TWL display font

        // Legacy support (keeping for existing components)
        'playfair': ['Playfair Display', 'serif'],
        'cinzel': ['Cinzel', 'serif'],
        'helvetica': ['Helvetica Neue', 'sans-serif'],
        'jetbrains': ['JetBrains Mono', 'monospace'],
      },
      fontSize: {
        // TWL Typography Scale (Mobile-First, Tailwind-Compatible)
        'xs': ['12px', '16px'],      // caption - Labels, badges
        'sm': ['14px', '20px'],      // body2 - Captions, metadata
        'base': ['16px', '24px'],    // body1 - Paragraphs, product details
        'lg': ['18px', '28px'],      // Enhanced body
        'xl': ['24px', '32px'],      // h4 - Feature titles
        '2xl': ['28px', '36px'],     // h3 - Subheaders
        '3xl': ['32px', '40px'],     // Enhanced h3
        '4xl': ['38px', '44px'],     // h2 - Section headings
        '5xl': ['46px', '52px'],     // h1 - Hero titles
        '6xl': ['56px', '64px'],     // Extra large hero
        '7xl': ['72px', '80px'],     // Display titles
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem',
        '3xl': '2rem',
      },
      boxShadow: {
        // TWL Hype Luxury Footwear Shadow System
        'xs': '0 1px 2px 0 rgba(26, 26, 26, 0.05)',
        'sm': '0 1px 3px 0 rgba(26, 26, 26, 0.1), 0 1px 2px 0 rgba(26, 26, 26, 0.06)',
        'DEFAULT': '0 4px 6px -1px rgba(26, 26, 26, 0.1), 0 2px 4px -1px rgba(26, 26, 26, 0.06)',
        'md': '0 10px 15px -3px rgba(26, 26, 26, 0.1), 0 4px 6px -2px rgba(26, 26, 26, 0.05)',
        'lg': '0 20px 25px -5px rgba(26, 26, 26, 0.1), 0 10px 10px -5px rgba(26, 26, 26, 0.04)',
        'xl': '0 25px 50px -12px rgba(26, 26, 26, 0.25)',
        'glass': '0 8px 32px 0 rgba(217, 217, 217, 0.15)', // Platinum Silver glass
        'glass-light': '0 4px 16px 0 rgba(249, 250, 251, 0.1)', // Ice White glass
        'glass-dark': '0 4px 16px 0 rgba(26, 26, 26, 0.1)', // Dark Grey glass
        'neon-glow': '0 0 20px rgba(191, 255, 0, 0.5)', // Neon Volt Lime glow
        'chrome-glow': '0 0 20px rgba(176, 176, 176, 0.3)', // Chrome Metallic glow
        'platinum-glow': '0 0 15px rgba(217, 217, 217, 0.4)', // Platinum Silver glow
        'success-glow': '0 0 20px rgba(40, 199, 111, 0.5)',
        'error-glow': '0 0 20px rgba(255, 76, 81, 0.5)',
        'inner': 'inset 0 2px 4px 0 rgba(26, 26, 26, 0.06)',
      },
      backdropBlur: {
        'xs': '2px',
        'sm': '4px',
        'md': '12px',
        'lg': '16px',
        'xl': '24px',
        '2xl': '40px',
        '3xl': '64px',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'pulse-neon': 'pulseNeon 2s ease-in-out infinite alternate',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'shimmer': 'shimmer 2s linear infinite',
        'gold-glow-pulse': 'goldGlowPulse 3s ease-in-out infinite',
        'emerald-glow-pulse': 'emeraldGlowPulse 3s ease-in-out infinite',
        'text-shimmer': 'textShimmer 3s linear infinite',
        'float-slow': 'float 8s ease-in-out infinite',
        'bounce-gentle': 'bounceGentle 4s ease-in-out infinite',
        'breathe': 'breathe 4s ease-in-out infinite',
        'morph': 'morph 6s ease-in-out infinite',
        'drift': 'drift 10s ease-in-out infinite',
        'ripple': 'ripple 2s ease-out',
        'cascade': 'cascade 1.5s ease-out',
        'emerge': 'emerge 2s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        glow: {
          '0%': { transform: 'scale(1)', boxShadow: '0 0 20px rgba(191, 255, 0, 0.5)' },
          '100%': { transform: 'scale(1.03)', boxShadow: '0 0 30px rgba(191, 255, 0, 0.8)' },
        },
        pulseNeon: {
          '0%': { boxShadow: '0 0 8px #BFFF00' },
          '100%': { boxShadow: '0 0 16px #BFFF00, 0 0 24px #BFFF00' },
        },
        slideUp: {
          '0%': { transform: 'translateY(100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
        goldGlowPulse: {
          '0%, 100%': { boxShadow: '0 0 20px rgba(255, 215, 0, 0.3)' },
          '50%': { boxShadow: '0 0 40px rgba(255, 215, 0, 0.8), 0 0 60px rgba(255, 215, 0, 0.4)' },
        },
        emeraldGlowPulse: {
          '0%, 100%': { boxShadow: '0 0 20px rgba(46, 75, 58, 0.3)' },
          '50%': { boxShadow: '0 0 40px rgba(46, 75, 58, 0.8), 0 0 60px rgba(46, 75, 58, 0.4)' },
        },
        textShimmer: {
          '0%': { backgroundPosition: '-200% center' },
          '100%': { backgroundPosition: '200% center' },
        },
        bounceGentle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
        breathe: {
          '0%, 100%': { transform: 'scale(1)', opacity: '0.8' },
          '50%': { transform: 'scale(1.05)', opacity: '1' },
        },
        morph: {
          '0%, 100%': { borderRadius: '50%', transform: 'rotate(0deg)' },
          '25%': { borderRadius: '25%', transform: 'rotate(90deg)' },
          '50%': { borderRadius: '10%', transform: 'rotate(180deg)' },
          '75%': { borderRadius: '25%', transform: 'rotate(270deg)' },
        },
        drift: {
          '0%, 100%': { transform: 'translateX(0) translateY(0)' },
          '25%': { transform: 'translateX(20px) translateY(-10px)' },
          '50%': { transform: 'translateX(-15px) translateY(-20px)' },
          '75%': { transform: 'translateX(-10px) translateY(10px)' },
        },
        ripple: {
          '0%': { transform: 'scale(0)', opacity: '1' },
          '100%': { transform: 'scale(4)', opacity: '0' },
        },
        cascade: {
          '0%': { transform: 'translateY(-20px)', opacity: '0' },
          '50%': { transform: 'translateY(-10px)', opacity: '0.5' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        emerge: {
          '0%': { transform: 'scale(0.8) rotateY(-90deg)', opacity: '0' },
          '50%': { transform: 'scale(1.05) rotateY(-45deg)', opacity: '0.7' },
          '100%': { transform: 'scale(1) rotateY(0deg)', opacity: '1' },
        },
      },
      // No gradients - using solid colors only
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
};
