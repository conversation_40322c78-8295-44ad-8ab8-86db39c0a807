'use client'

import { useState, useEffect } from 'react'
import { useSimpleCart, useSimpleWishlist, useSimpleAuth } from '../providers/TWLProviders'

export default function InteractiveHeader() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [isNavMenuVisible, setIsNavMenuVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)
  const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState(false)
  const [isCurrencyDropdownOpen, setIsCurrencyDropdownOpen] = useState(false)
  const [selectedLanguage, setSelectedLanguage] = useState('es-MX')
  const [selectedCurrency, setSelectedCurrency] = useState('MXN')

  // Use context providers
  const { getItemsCount, showToast: showCartToast } = useSimpleCart()
  const { getWishlistCount, showToast: showWishlistToast } = useSimpleWishlist()
  const { user, isAuthenticated } = useSimpleAuth()

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY
      setIsScrolled(currentScrollY > 10)

      // Hide nav menu when scrolling down, show when scrolling up
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsNavMenuVisible(false)
      } else {
        setIsNavMenuVisible(true)
      }

      setLastScrollY(currentScrollY)
    }

    const handleClickOutside = (event) => {
      // Close dropdowns when clicking outside
      if (!event.target.closest('.language-dropdown') && !event.target.closest('.currency-dropdown')) {
        setIsLanguageDropdownOpen(false)
        setIsCurrencyDropdownOpen(false)
      }
    }

    window.addEventListener('scroll', handleScroll)
    document.addEventListener('click', handleClickOutside)

    return () => {
      window.removeEventListener('scroll', handleScroll)
      document.removeEventListener('click', handleClickOutside)
    }
  }, [lastScrollY])

  // Language and currency options
  const languages = [
    { code: 'es-MX', name: 'Español', flag: '🇲🇽', country: 'México' },
    { code: 'en-US', name: 'English', flag: '🇺🇸', country: 'United States' },
    { code: 'pt-BR', name: 'Português', flag: '🇧🇷', country: 'Brasil' }
  ]

  const currencies = [
    { code: 'MXN', name: 'Peso Mexicano', symbol: '$', flag: '🇲🇽' },
    { code: 'USD', name: 'US Dollar', symbol: '$', flag: '🇺🇸' },
    { code: 'BRL', name: 'Real Brasileiro', symbol: 'R$', flag: '🇧🇷' }
  ]

  // Navigation categories
  const navCategories = [
    { name: 'Limited Editions', href: '/limited-editions', isSpecial: true },
    { name: 'Brands', href: '/brands' },
    { name: 'Women', href: '/women' },
    { name: 'Men', href: '/men' },
    { name: 'Kids', href: '/kids' },
    { name: 'On The Side', href: '/on-the-side' }
  ]

  const handleSearch = (e) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`
    }
  }

  // Get current counts from context
  const cartCount = getItemsCount()
  const wishlistCount = getWishlistCount()

  const handleCartClick = () => {
    // For demo purposes, show cart info
    showCartToast(`Tienes ${cartCount} productos en tu carrito`)
  }

  const handleWishlistClick = () => {
    // For demo purposes, show wishlist info
    showWishlistToast(`Tienes ${wishlistCount} productos en favoritos`)
  }

  const handleLanguageChange = (langCode) => {
    setSelectedLanguage(langCode)
    setIsLanguageDropdownOpen(false)
    // Here you would implement actual language switching logic
    console.log('Language changed to:', langCode)
  }

  const handleCurrencyChange = (currencyCode) => {
    setSelectedCurrency(currencyCode)
    setIsCurrencyDropdownOpen(false)
    // Here you would implement actual currency switching logic
    console.log('Currency changed to:', currencyCode)
  }

  return (
    <>
      {/* Main Header */}
      <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-white/95 backdrop-blur-xl border-b border-white/30 shadow-lg'
          : 'bg-white/90 backdrop-blur-xl border-b border-white/20 shadow-lg'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <div className="flex-shrink-0">
            <a href="/" className="flex items-center space-x-3 hover:opacity-90 transition-opacity">
              <div className="w-10 h-10 bg-gradient-to-br from-lime-400 to-lime-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-shadow">
                <span className="text-black font-bold text-xl">W</span>
              </div>
              <div className="hidden sm:block">
                <span className="text-xl font-bold text-black">
                  The White Laces
                </span>
                <div className="text-xs text-gray-500 font-medium">
                  Luxury Streetwear
                </div>
              </div>
            </a>
          </div>

          {/* Center Search Bar */}
          <div className="hidden lg:flex flex-1 max-w-lg mx-8">
            <form onSubmit={handleSearch} className="relative w-full group">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Buscar productos, marcas..."
                className="w-full px-4 py-3 pl-12 pr-4 text-gray-700 bg-white/70 backdrop-blur-sm border border-white/40 rounded-full focus:outline-none focus:ring-2 focus:ring-lime-500 focus:border-transparent focus:bg-white/90 transition-all duration-300 placeholder-gray-500"
              />
              <button type="submit" className="absolute inset-y-0 left-0 flex items-center pl-4">
                <svg className="w-5 h-5 text-gray-400 group-focus-within:text-lime-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </form>
          </div>

          {/* Navigation */}
          <nav className="hidden xl:flex items-center space-x-8">
            <a href="/shop" className="text-gray-700 hover:text-lime-600 transition-colors font-medium relative group">
              Tienda
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-lime-500 transition-all duration-300 group-hover:w-full"></span>
            </a>
            <a href="/ai-features" className="text-gray-700 hover:text-lime-600 transition-colors font-medium relative group">
              IA
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-lime-500 transition-all duration-300 group-hover:w-full"></span>
            </a>
            <a href="/community" className="text-gray-700 hover:text-lime-600 transition-colors font-medium relative group">
              Social
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-lime-500 transition-all duration-300 group-hover:w-full"></span>
            </a>
          </nav>

          {/* Actions */}
          <div className="flex items-center space-x-2">
            {/* Search - Mobile only */}
            <button 
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden p-2 text-gray-700 hover:text-lime-600 hover:bg-lime-50 rounded-full transition-all duration-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>

            {/* Wishlist */}
            <button
              onClick={handleWishlistClick}
              className="relative p-2 text-gray-700 hover:text-lime-600 hover:bg-lime-50 rounded-full transition-all duration-300 group"
              title="Lista de favoritos"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              {wishlistCount > 0 && (
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-lime-500 text-black text-xs font-bold rounded-full flex items-center justify-center">
                  {wishlistCount}
                </span>
              )}
            </button>

            {/* Cart */}
            <button
              onClick={handleCartClick}
              className="relative p-2 text-gray-700 hover:text-lime-600 hover:bg-lime-50 rounded-full transition-all duration-300 group"
              title="Carrito de compras"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
              {cartCount > 0 && (
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-lime-500 text-black text-xs font-bold rounded-full flex items-center justify-center animate-pulse">
                  {cartCount}
                </span>
              )}
            </button>

            {/* Account */}
            <button
              className="p-2 text-gray-700 hover:text-lime-600 hover:bg-lime-50 rounded-full transition-all duration-300"
              title={isAuthenticated ? `Hola, ${user?.firstName || 'Usuario'}` : 'Iniciar sesión'}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              {isAuthenticated && (
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full"></span>
              )}
            </button>

            {/* Mobile Menu */}
            <button 
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="xl:hidden p-2 text-gray-700 hover:text-lime-600 hover:bg-lime-50 rounded-full transition-all duration-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="xl:hidden bg-white/95 backdrop-blur-xl border-t border-white/20 shadow-lg">
          <div className="px-4 py-6 space-y-4">
            <form onSubmit={handleSearch} className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Buscar productos..."
                className="w-full px-4 py-3 pl-10 pr-4 text-gray-700 bg-white/70 border border-white/40 rounded-full focus:outline-none focus:ring-2 focus:ring-lime-500"
              />
              <button type="submit" className="absolute inset-y-0 left-0 flex items-center pl-3">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </form>

            {/* Main Navigation */}
            <div className="space-y-2 mb-6">
              <h3 className="text-sm font-semibold text-gray-900 px-4 mb-3">Navegación Principal</h3>
              {[
                { name: 'Tienda', href: '/shop' },
                { name: 'IA Features', href: '/ai-features' },
                { name: 'Social', href: '/community' }
              ].map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="block px-4 py-3 text-gray-700 hover:text-lime-600 hover:bg-lime-50 rounded-lg transition-colors font-medium"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.name}
                </a>
              ))}
            </div>

            {/* Category Navigation */}
            <div className="space-y-2 mb-6">
              <h3 className="text-sm font-semibold text-gray-900 px-4 mb-3">Categorías</h3>
              {navCategories.map((category) => (
                <a
                  key={category.name}
                  href={category.href}
                  className={`block px-4 py-3 rounded-lg transition-colors font-medium ${
                    category.isSpecial
                      ? 'text-red-600 hover:text-red-700 hover:bg-red-50'
                      : 'text-gray-700 hover:text-lime-600 hover:bg-lime-50'
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {category.name}
                </a>
              ))}
            </div>

            {/* Language and Currency Selection */}
            <div className="border-t border-gray-200 pt-4">
              <h3 className="text-sm font-semibold text-gray-900 px-4 mb-3">Preferencias</h3>

              {/* Mobile Language Selection */}
              <div className="px-4 py-2">
                <label className="text-xs text-gray-500 mb-2 block">Idioma</label>
                <div className="space-y-1">
                  {languages.map((language) => (
                    <button
                      key={language.code}
                      onClick={() => {
                        handleLanguageChange(language.code)
                        setIsMobileMenuOpen(false)
                      }}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors ${
                        selectedLanguage === language.code ? 'bg-lime-50 text-lime-600' : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <span className="text-lg">{language.flag}</span>
                      <span className="font-medium">{language.name}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Mobile Currency Selection */}
              <div className="px-4 py-2">
                <label className="text-xs text-gray-500 mb-2 block">Moneda</label>
                <div className="space-y-1">
                  {currencies.map((currency) => (
                    <button
                      key={currency.code}
                      onClick={() => {
                        handleCurrencyChange(currency.code)
                        setIsMobileMenuOpen(false)
                      }}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors ${
                        selectedCurrency === currency.code ? 'bg-lime-50 text-lime-600' : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <span className="text-lg">{currency.flag}</span>
                      <span className="font-medium">{currency.code} - {currency.symbol}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      </header>

      {/* Second Navigation Bar - Category Menu */}
      <nav className={`fixed left-0 right-0 z-40 transition-all duration-300 ${
        isNavMenuVisible ? 'top-16 lg:top-20' : '-top-16'
      } ${
        isScrolled
          ? 'bg-white/90 backdrop-blur-lg border-b border-gray-200/50'
          : 'bg-white/80 backdrop-blur-md border-b border-gray-200/30'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Category Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              {navCategories.map((category) => (
                <a
                  key={category.name}
                  href={category.href}
                  className={`text-sm font-medium transition-colors duration-200 relative group ${
                    category.isSpecial
                      ? 'text-red-600 hover:text-red-700'
                      : 'text-gray-700 hover:text-lime-600'
                  }`}
                >
                  {category.name}
                  <span className={`absolute bottom-0 left-0 w-0 h-0.5 transition-all duration-300 group-hover:w-full ${
                    category.isSpecial ? 'bg-red-500' : 'bg-lime-500'
                  }`}></span>
                </a>
              ))}
            </div>

            {/* Mobile Category Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden flex items-center space-x-2 text-gray-700 hover:text-lime-600 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
              <span className="text-sm font-medium">Categorías</span>
            </button>

            {/* Language and Currency Dropdowns */}
            <div className="flex items-center space-x-4">
              {/* Language Dropdown */}
              <div className="relative language-dropdown">
                <button
                  onClick={() => {
                    setIsLanguageDropdownOpen(!isLanguageDropdownOpen)
                    setIsCurrencyDropdownOpen(false)
                  }}
                  className="flex items-center space-x-2 px-3 py-1.5 text-sm text-gray-700 hover:text-lime-600 hover:bg-lime-50 rounded-lg transition-all duration-200"
                >
                  <span className="text-lg">{languages.find(lang => lang.code === selectedLanguage)?.flag}</span>
                  <span className="hidden sm:block font-medium">{languages.find(lang => lang.code === selectedLanguage)?.name}</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* Language Dropdown Menu */}
                {isLanguageDropdownOpen && (
                  <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                    {languages.map((language) => (
                      <button
                        key={language.code}
                        onClick={() => handleLanguageChange(language.code)}
                        className={`w-full flex items-center space-x-3 px-4 py-2 text-sm hover:bg-lime-50 transition-colors ${
                          selectedLanguage === language.code ? 'bg-lime-50 text-lime-600' : 'text-gray-700'
                        }`}
                      >
                        <span className="text-lg">{language.flag}</span>
                        <div className="text-left">
                          <div className="font-medium">{language.name}</div>
                          <div className="text-xs text-gray-500">{language.country}</div>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Currency Dropdown */}
              <div className="relative currency-dropdown">
                <button
                  onClick={() => {
                    setIsCurrencyDropdownOpen(!isCurrencyDropdownOpen)
                    setIsLanguageDropdownOpen(false)
                  }}
                  className="flex items-center space-x-2 px-3 py-1.5 text-sm text-gray-700 hover:text-lime-600 hover:bg-lime-50 rounded-lg transition-all duration-200"
                >
                  <span className="text-lg">{currencies.find(curr => curr.code === selectedCurrency)?.flag}</span>
                  <span className="font-medium">{selectedCurrency}</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* Currency Dropdown Menu */}
                {isCurrencyDropdownOpen && (
                  <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                    {currencies.map((currency) => (
                      <button
                        key={currency.code}
                        onClick={() => handleCurrencyChange(currency.code)}
                        className={`w-full flex items-center space-x-3 px-4 py-2 text-sm hover:bg-lime-50 transition-colors ${
                          selectedCurrency === currency.code ? 'bg-lime-50 text-lime-600' : 'text-gray-700'
                        }`}
                      >
                        <span className="text-lg">{currency.flag}</span>
                        <div className="text-left">
                          <div className="font-medium">{currency.code} - {currency.symbol}</div>
                          <div className="text-xs text-gray-500">{currency.name}</div>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </nav>
    </>
  )
}
