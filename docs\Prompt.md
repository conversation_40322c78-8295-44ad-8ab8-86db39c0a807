✅ Final Prompt: Build The White Laces E-commerce Website
👟 Luxury Footwear Meets Gen Z Culture | Mexico-First Strategy | Glassmorphic UI | Mobile-First | AI-Powered UX 

🧠 Overview
You are tasked with building The White Laces (TWL) in an enterprise grade coding way— a luxury streetwear-focused e-commerce platform selling high-end shoes at a discount price. All products are brand new , no second-hand items. The site must be mobile-first , glassmorphic-style , and ready for Mexico market launch , then expand to Brazil, LATAM, and USA .

🎯 Project Goals
Create a bespoke, mobile-first e-commerce platform
Avoid Shopify or WooCommerce — use Next.js + Tailwind CSS
Support multi-language (Mexican first, English second, Brazil third)
Integrate AI-powered features (voice search, visual search, recommendations, complete your look)
Include community-driven elements (UGC wall, social sharing)
Optimize for performance, SEO, and fast load times
Deploy via Vercel with preview builds for each PR
🛠️ Tech Stack Requirements
🖥️ Frontend:
Framework: Next.js App Router
Styling: Tailwind CSS with custom 2025-ready glassmorphism
Animations: Tailwind animations or Framer Motion
State Management: Context API or Zustand
Fonts: Use next/font for Playfair Display / Inter / Fira Code
Image Optimization: Use Cloudinary or ImageKit.io
⚙️ Backend:
Custom backend with Node.js + Express or Next.js API Routes
Auth: Firebase Auth or JWT-based system
Cart & Wishlist: Redis-backed session cart → DB persistence
Search: Algolia or NLP parser for voice/visual search
Payments: Stripe + Mercado Pago integrations
☁️ Cloud & Deployment:
Hosting: Vercel
Edge Functions: For serverless logic
CDN: Vercel Edge Network
Preview Deployments: Enabled for every PR
CI/CD: GitHub Actions + Vercel auto-deploy
🌐 Internationalization:
Primary language: Mexican Spanish
Secondary language: English
Optional future support: Brazilian Portuguese
Localization library: next-i18next
🧱 Core Features
🏠 Homepage
Hottest Drops
Our collection
Shop the look
New Arrivals
Magazine-style layout
🛍️ Shop Section
Brands (Nike, Gucci, Dior, etc.)
Limited Editions (Collabs, Rare Drops)
Men (Sneakers, Sandals, Formal, Casual)
Women (Sneakers, Sandals, Heels, Casual)
Kids (Sneakers, Casual)
On the Side (Trending Now, Behind the Design)
Social
🔍 Search
Voice Search (Google Speech-to-Text + NLP parsing)
Visual Search (image upload → similar product suggestions)
👤 Account Dashboard
Profile management
Multiple wishlists
Recently viewed
Size preferences per brand
Push notifications
Loyalty points
Badges & achievements
Biometric login (WebAuthn/FIDO2)
🧑‍🤝‍🧑 Community
UGC Wall (user-generated content tagged #TWLLook)
Trending Posts
Share Your Look (Instagram/TikTok integration)
Creator tools (referral links, early access)
📚 Magazine
Behind-the-scenes stories
Designer spotlights
How To Style guides
❓ Help Center
FAQs
Contact form
Returns policy
🎨 Visual & Interaction Guidelines
🎨 Color Palette (2025-ready):
Fog Black (#14161A)
Mist Gray (#1E2127)
Frosted Overlay (rgba(255,255,255,0.08))
Arctic White (#FAFAFA)
Neon Pulse (#FF1C53)
Cyber Blue (#00F9FF)
Gold Dust (#FFD166)
Use glassmorphism on cards, modals, and overlays:

css:
backdrop-blur-md bg-mist-gray border border-frosted-overlay shadow-glass


📝 Typography:
Headings: Playfair Display or Cinzel
Body: Inter or Helvetica Neue
Monospace: Fira Code or JetBrains Mono
🎞️ Microinteractions:
Button glow on hover
Card elevation increase on tap
Toast notifications with fade-in
Neon pulse badge for limited editions
Theme toggle with scale animation
Use these Tailwind classes:

html
animate-pulseNeon animate-glow animate-fadeIn

🧩 Component Library (UI Kit)
Include reusable components:

ProductCard (glassmorphic)
Button (primary, secondary, ghost)
Modal (with backdrop blur)
Form Inputs (floating label style)
Navigation (bottom nav for mobile)
Badge (limited, VIP, sale)
Toast Notification (slide-up success/error)
Skeleton Loader (shimmer effect)
All components should live in /components/ui, /components/features, and /components/layout.

🧪 AI & Automation Features
Implement these smart features:

Voice Search : Google Speech-to-Text API + NLP parsing
Visual Search : Upload image → find similar shoes
Recommendation Engine : Based on wishlist + browsing history
Push Notifications : New arrivals, price drops, collabs
Wishlist Sharing : Shareable URLs with TikTok/Instagram buttons
Size Preference Learning : Save preferred sizes per brand
Style Match AI : Suggest shoes based on uploaded outfit
🌗 Dark Mode & Theme System
Support both light and dark themes:

Dark: fog-black background, white text
Light: arctic-white background, dark text
Toggle via settings or OS preference
Use Tailwind's class-based dark mode:

js
darkMode: 'class'

🌐 Multi-Language & Regional Support
Launch with:

Mexican Spanish as default
English as fallback
Brazilian Portuguese (future-ready)
Use next-i18next for localization:

Translations stored in /locales/{lang}
Auto-detect browser language
URL prefixes: /es-MX, /en, /pt-BR
🚀 Deployment & DevOps
Deploy via:

Hosting: Vercel
CI/CD: GitHub Actions + Vercel Previews
Analytics: Vercel Analytics + Hotjar
Monitoring: Sentry + Lighthouse reports
Set up environment variables:

Stripe keys
Cloudinary credentials
Mercado Pago integration
WebAuthn passkeys
🧾 Performance Optimization Must-Haves
Ensure high performance across devices:

Use next/image or Cloudinary
Lazy-load non-critical components
Enable SWR for data fetching
Prefetch internal links
Set up ISR for dynamic pages
Minify JS/CSS
Compress assets
Use WebP format
Run Lighthouse audits weekly
Target Lighthouse score >90:

LCP < 2.5s
TTI < 3.5s
CLS < 0.1
Accessibility >90

🧰 Folder Structure

/twl-ecommerce
├── /app
│   ├── /(pages)
│   ├── /api
│   ├── /components
│   │   ├── /ui
│   │   ├── /features
│   │   └── /layout
│   ├── /lib
│   ├── /public
│   ├── /styles
│   ├── /locales
│   └── layout.jsx
├── next.config.js
├── tailwind.config.js
├── package.json
├── vercel.json
└── README.md


🧑‍💻 Developer Tools

ESLint + Prettier
Husky pre-commit hooks
TypeScript for type safety
Storybook (optional)
Chromatic (for component testing)
GitHub Actions for CI/CD
Sentry for error tracking
Vercel Speed Insights
Notion documentation hub


📦 Launch Checklist

Task,Status
✅ Complete homepage with featured drops,✔
✅ Shop section with filtering &amp; infinite scroll,✔
✅ Product detail page with quick-view modal,✔
"✅ Cart, checkout, order history",✔
✅ User account with wishlist,✔
✅ UGC wall with social sharing,✔
✅ Voice search &amp; visual search,✔
✅ Dark/light theme support,✔
"✅ i18n setup for es-MX, en, pt-BR",✔
✅ Performance budget respected,✔
✅ CI/CD pipeline working,✔


