# 🧩 TWL Shop Page - Component Documentation

**Date:** 2025-06-21  
**Status:** ✅ Complete Documentation  
**Components:** 5 Core Components  

## 📋 **Component Overview**

This document provides detailed documentation for all components used in the TWL Shop Page implementation.

## 🛍️ **1. ShopPage Component**

### **Location:** `app/shop/page.jsx`
### **Type:** Page Component (Client-side)
### **Purpose:** Main shop page container with product grid and filtering

### **Props:** None (Page component)

### **State Management:**
```javascript
const [products, setProducts] = useState([])
const [isFilterOpen, setIsFilterOpen] = useState(false)
```

### **Key Features:**
- **Product Grid**: Responsive 2-5 column layout
- **Filter Integration**: Overlay filter system
- **Infinite Scroll**: Performance-optimized pagination
- **Search Results**: Dynamic result counting
- **Mobile Optimization**: Touch-friendly interface

### **Dependencies:**
- `useAdvancedFilters` - Filter logic
- `useInfiniteScroll` - Pagination
- `EnhancedFilterSidebar` - Filter component
- `OptimizedProductCard` - Product display

### **Responsive Behavior:**
```javascript
// Grid breakpoints
"grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-5"
```

### **Performance Features:**
- **Priority Loading**: First 8 products prioritized
- **Lazy Loading**: Images load as needed
- **Memoized Filtering**: Optimized filter operations
- **Infinite Scroll**: Loads 12 products at a time

### **UI Enhancements (Latest):**
- **Enhanced Heart Icons**: Larger size (w-6 h-6), dark red outline, red hover, lime green filled
- **Pulsating Cart Button**: Hover-only circular pulsating effect with lime green color
- **Improved Accessibility**: Better touch targets and visual feedback
- **Professional Styling**: Consistent with luxury streetwear aesthetic

---

## 🎛️ **2. EnhancedFilterSidebar Component**

### **Location:** `components/shop/EnhancedFilterModule.jsx`
### **Type:** Interactive Sidebar Component
### **Purpose:** Advanced filtering system with overlay behavior

### **Props:**
```javascript
{
  searchQuery, setSearchQuery,
  selectedCategory, setSelectedCategory,
  selectedBrand, setSelectedBrand,
  sortBy, setSortBy,
  priceRange, setPriceRange,
  selectedSizes, setSelectedSizes,
  selectedColors, setSelectedColors,
  isOnSale, setIsOnSale,
  isLimited, setIsLimited,
  isNew, setIsNew,
  onClearAll,
  isOpen, setIsOpen
}
```

### **State Management:**
```javascript
const [activeFilters, setActiveFilters] = useState(0)
const [expandedSections, setExpandedSections] = useState({
  categories: true,
  brands: false,
  price: false,
  sizes: false,
  colors: false
})
```

### **Filter Sections:**
1. **Quick Filters**: En Oferta, Edición Limitada, Nuevos
2. **Search Bar**: Text-based product search
3. **Categories**: Product category selection
4. **Brands**: Brand filtering with counts
5. **Price Range**: Dual-range slider
6. **Sizes**: Grid-based size selection
7. **Colors**: Visual color swatches
8. **Sort Options**: Multiple sorting criteria

### **Animation Features:**
- **Slide Animation**: Smooth slide-in/out
- **Section Expansion**: Collapsible sections
- **Backdrop**: Dark overlay when open
- **Transitions**: Smooth state transitions

### **Responsive Design:**
- **Mobile**: Full-screen overlay
- **Desktop**: Fixed sidebar overlay
- **Touch Targets**: Optimized for touch
- **Accessibility**: Keyboard navigation

---

## 🃏 **3. OptimizedProductCard Component**

### **Location:** `components/performance/OptimizedProductCard.jsx`
### **Type:** Product Display Component
### **Purpose:** Optimized product card with animations

### **Props:**
```javascript
{
  product: {
    id, name, brand, category, price, originalPrice,
    isLimited, isNew, description, sizes, colors,
    popularity, image, hoverImage
  },
  priority: boolean // For image loading priority
}
```

### **Features:**
- **Image Optimization**: Next.js Image component
- **Hover Effects**: Pop-up animation on hover
- **Price Display**: Original and sale prices
- **Quick Actions**: Add to cart, wishlist
- **Responsive**: Mobile and desktop optimized

### **Animation System:**
```javascript
// Pop-up hover effect
className="card-popup"
// CSS: transform: translateY(-8px) scale(1.02)
```

### **Performance Optimizations:**
- **Priority Loading**: Critical images load first
- **Lazy Loading**: Non-critical images lazy load
- **Memoization**: Prevents unnecessary re-renders
- **GPU Acceleration**: Hardware-accelerated animations

---

## 🔄 **4. BackToTop Component**

### **Location:** `components/ui/BackToTop.jsx`
### **Type:** Utility Component
### **Purpose:** Smooth scroll to top functionality

### **Features:**
- **Scroll Detection**: Shows/hides based on scroll position
- **Smooth Animation**: CSS smooth scroll behavior
- **Fixed Position**: Bottom-right corner positioning
- **Accessibility**: Keyboard accessible

### **Implementation:**
```javascript
// Scroll to top function
const scrollToTop = () => {
  window.scrollTo({ top: 0, behavior: 'smooth' })
}
```

---

## 🎨 **5. ProductCardSkeleton Component**

### **Location:** `components/ui/ProductCardSkeleton.jsx`
### **Type:** Loading State Component
### **Purpose:** Skeleton loading for product cards

### **Props:**
```javascript
{
  count: number // Number of skeleton cards to show
}
```

### **Features:**
- **Shimmer Animation**: Loading animation effect
- **Responsive**: Matches product card dimensions
- **Performance**: Lightweight loading state
- **Accessibility**: Screen reader friendly

---

## 🎣 **Custom Hooks Documentation**

### **useAdvancedFilters Hook**
```javascript
// Returns comprehensive filter state and functions
const {
  searchQuery, setSearchQuery,
  selectedCategory, setSelectedCategory,
  selectedBrand, setSelectedBrand,
  sortBy, setSortBy,
  priceRange, setPriceRange,
  selectedSizes, setSelectedSizes,
  selectedColors, setSelectedColors,
  isOnSale, setIsOnSale,
  isLimited, setIsLimited,
  isNew, setIsNew,
  filteredProducts,
  filterStats,
  activeFilters,
  clearAllFilters
} = useAdvancedFilters(products)
```

### **useInfiniteScroll Hook**
```javascript
// Returns paginated items and loading state
const {
  displayedItems,
  hasMore,
  isLoading,
  reset
} = useInfiniteScroll(items, itemsPerPage)
```

---

## 🎯 **Component Integration**

### **Data Flow:**
```
ShopPage
├── Products State (useState)
├── Filter State (useAdvancedFilters)
├── Pagination (useInfiniteScroll)
└── Components
    ├── EnhancedFilterSidebar (Filter UI)
    ├── OptimizedProductCard (Product Display)
    ├── ProductCardSkeleton (Loading State)
    └── BackToTop (Utility)
```

### **State Management Pattern:**
1. **Local State**: UI-specific state (modals, toggles)
2. **Custom Hooks**: Business logic (filtering, pagination)
3. **Props Drilling**: Parent-to-child communication
4. **Context**: Global state (cart, wishlist)

---

## 🔧 **Component Best Practices**

### **Performance:**
- **Memoization**: React.memo for expensive components
- **Lazy Loading**: Dynamic imports for large components
- **Virtualization**: For large lists (future enhancement)
- **Code Splitting**: Route-based splitting

### **Accessibility:**
- **Semantic HTML**: Proper HTML structure
- **ARIA Labels**: Screen reader support
- **Keyboard Navigation**: Full keyboard support
- **Focus Management**: Proper focus handling

### **Maintainability:**
- **Single Responsibility**: Each component has one purpose
- **Prop Validation**: TypeScript or PropTypes
- **Documentation**: Comprehensive component docs
- **Testing**: Unit and integration tests

---

## 📱 **Mobile Considerations**

### **Touch Optimization:**
- **Touch Targets**: Minimum 44px touch areas
- **Gesture Support**: Swipe and pinch gestures
- **Performance**: Optimized for mobile devices
- **Battery**: Efficient animations and rendering

### **Responsive Design:**
- **Breakpoints**: Tailwind responsive system
- **Flexible Layouts**: CSS Grid and Flexbox
- **Image Optimization**: Responsive images
- **Typography**: Scalable text sizes

---

**📝 Note:** All components follow TWL design system guidelines and modern React best practices for optimal performance and maintainability.
