# 🔧 TWL Shop Page - Troubleshooting Guide

**Date:** 2025-06-21  
**Status:** ✅ Complete Troubleshooting Guide  
**Coverage:** Common Issues & Solutions  

## 📋 **Quick Diagnostics Checklist**

Before diving into specific issues, run through this quick checklist:

- [ ] Development server is running (`npm run dev`)
- [ ] No console errors in browser developer tools
- [ ] All dependencies are installed (`npm install`)
- [ ] Godber font file exists at `/public/fonts/Godber-Regular.ttf`
- [ ] Tailwind CSS is compiling correctly
- [ ] Browser cache is cleared

## 🚨 **Common Issues & Solutions**

### **1. Filter Sidebar Not Showing**

**Symptoms:**
- Filter button doesn't open sidebar
- Sidebar appears but is not visible
- Animation not working

**Solutions:**
```javascript
// Check if state is properly managed
const [isFilterOpen, setIsFilterOpen] = useState(false)

// Verify z-index is high enough
className="fixed top-0 left-0 h-screen w-80 bg-white shadow-2xl z-50"

// Ensure backdrop is properly positioned
style={{ top: '80px', height: 'calc(100vh - 80px)' }}
```

**Debug Steps:**
1. Check browser console for JavaScript errors
2. Verify Framer Motion is installed: `npm list framer-motion`
3. Test state changes in React DevTools
4. Check CSS z-index conflicts

### **2. Godber Font Not Loading**

**Symptoms:**
- "Tienda TWL" text shows fallback font
- Font file 404 error in network tab
- Text appears in serif instead of Godber

**Solutions:**
```css
/* Verify font-face declaration in globals.css */
@font-face {
  font-family: 'Godber';
  src: url('/fonts/Godber-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
```

**Debug Steps:**
1. Check if font file exists: `/public/fonts/Godber-Regular.ttf`
2. Verify network tab shows successful font loading
3. Test font-family in browser DevTools
4. Clear browser cache and hard refresh

### **3. Product Grid Layout Issues**

**Symptoms:**
- Products not displaying in correct columns
- Grid breaks on certain screen sizes
- Cards overlapping or misaligned

**Solutions:**
```javascript
// Verify responsive grid classes
className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-5 gap-6"

// Check container width
className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
```

**Debug Steps:**
1. Test on different screen sizes
2. Check Tailwind CSS compilation
3. Verify no conflicting CSS rules
4. Test with browser responsive mode

### **4. Icons Not Displaying**

**Symptoms:**
- SVG icons appear as broken or missing
- Icons show as text or squares
- Inconsistent icon rendering

**Solutions:**
```javascript
// Verify SVG structure
<svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="..." />
</svg>

// Check icon imports
import { ChevronDownIcon } from '@heroicons/react/24/outline'
```

**Debug Steps:**
1. Inspect SVG elements in browser DevTools
2. Verify stroke and fill properties
3. Check for CSS conflicts affecting SVGs
4. Test icon libraries are properly installed

### **5. Performance Issues**

**Symptoms:**
- Slow page loading
- Laggy animations
- High memory usage
- Poor mobile performance

**Solutions:**
```javascript
// Optimize image loading
<Image
  src={product.image}
  alt={product.name}
  priority={index < 8} // Prioritize first 8 images
  loading={index < 8 ? 'eager' : 'lazy'}
/>

// Implement proper memoization
const filteredProducts = useMemo(() => {
  return products.filter(/* filter logic */)
}, [products, filters])
```

**Debug Steps:**
1. Run Lighthouse performance audit
2. Check Network tab for large assets
3. Monitor memory usage in DevTools
4. Test on slower devices/connections

### **6. Filter State Not Updating**

**Symptoms:**
- Filters don't affect product display
- State changes not reflected in UI
- Filter counts incorrect

**Solutions:**
```javascript
// Verify state updates
const handleCategoryChange = (categoryId) => {
  setSelectedCategory(categoryId)
  // Ensure state is properly updated
}

// Check filter logic
const filteredProducts = useMemo(() => {
  let filtered = products
  
  if (selectedCategory !== 'all') {
    filtered = filtered.filter(p => p.category === selectedCategory)
  }
  
  return filtered
}, [products, selectedCategory, /* other dependencies */])
```

**Debug Steps:**
1. Use React DevTools to monitor state
2. Add console.log to track state changes
3. Verify all dependencies in useMemo
4. Check for state mutation issues

## 🔍 **Advanced Debugging**

### **Browser Developer Tools**

**Console Tab:**
- Check for JavaScript errors
- Monitor state changes with console.log
- Test component functions manually

**Network Tab:**
- Verify all assets load correctly
- Check font file loading
- Monitor API calls (if any)

**Performance Tab:**
- Analyze rendering performance
- Identify performance bottlenecks
- Monitor memory usage

**Elements Tab:**
- Inspect CSS styles
- Check element positioning
- Verify responsive behavior

### **React Developer Tools**

**Components Tab:**
- Monitor component state
- Check prop values
- Verify component hierarchy

**Profiler Tab:**
- Analyze component render times
- Identify unnecessary re-renders
- Optimize component performance

## 🚀 **Performance Optimization**

### **Image Optimization**
```javascript
// Use Next.js Image component
import Image from 'next/image'

// Optimize loading strategy
<Image
  src={product.image}
  alt={product.name}
  width={400}
  height={400}
  priority={index < 8}
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

### **Code Splitting**
```javascript
// Dynamic imports for large components
const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <LoadingSkeleton />
})
```

### **Memoization**
```javascript
// Memoize expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data)
}, [data])

// Memoize components
const MemoizedComponent = React.memo(Component)
```

## 📱 **Mobile-Specific Issues**

### **Touch Events**
```javascript
// Handle touch events properly
const handleTouchStart = (e) => {
  // Touch handling logic
}

// Ensure touch targets are large enough
className="min-h-[44px] min-w-[44px]" // 44px minimum
```

### **Viewport Issues**
```html
<!-- Ensure proper viewport meta tag -->
<meta name="viewport" content="width=device-width, initial-scale=1.0">
```

### **iOS Safari Specific**
```css
/* Fix iOS Safari 100vh issue */
.full-height {
  height: 100vh;
  height: -webkit-fill-available;
}
```

## 🔄 **Recovery Procedures**

### **Complete Reset**
1. Stop development server
2. Clear node_modules: `rm -rf node_modules`
3. Clear package-lock.json: `rm package-lock.json`
4. Reinstall dependencies: `npm install`
5. Clear browser cache
6. Restart development server: `npm run dev`

### **Restore from Backup**
1. Copy files from backup directory
2. Verify all files are in correct locations
3. Run `npm install` to ensure dependencies
4. Restart development server
5. Test all functionality

### **Git Reset (if using version control)**
```bash
# Reset to last working commit
git reset --hard HEAD~1

# Or reset to specific commit
git reset --hard <commit-hash>
```

## 📞 **Getting Help**

### **Error Reporting**
When reporting issues, include:
- Browser and version
- Device type and OS
- Console error messages
- Steps to reproduce
- Expected vs actual behavior

### **Useful Resources**
- **Next.js Documentation**: https://nextjs.org/docs
- **Tailwind CSS Documentation**: https://tailwindcss.com/docs
- **Framer Motion Documentation**: https://www.framer.com/motion/
- **React Documentation**: https://react.dev/

---

**📝 Note:** This troubleshooting guide covers the most common issues. For complex problems, refer to the implementation notes and component documentation for deeper technical details.
